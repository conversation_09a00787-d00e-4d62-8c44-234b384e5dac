<?php
/**
 * WHMCS Knowledge Base Module
 * Custom module for storing and managing knowledge base articles in WHMCS
 * 
 * This module creates custom database tables for searchable support documentation
 * optimized for SEO and user experience
 */

if (!defined("WHMCS"))
    die("This file cannot be accessed directly");

use WHMCS\Database\Capsule;

class WhmcsKnowledgeBaseModule {
    
    private $config;
    
    public function __construct($whmcsConfig = null) {
        if ($whmcsConfig) {
            $this->config = $whmcsConfig;
        } else {
            $this->config = require_once 'whmcs-config.php';
        }
    }
    
    /**
     * Create knowledge base tables in WHMCS database
     */
    public function installModule() {
        try {
            // Create knowledge base categories table
            Capsule::schema()->create('mod_kb_categories', function ($table) {
                $table->increments('id');
                $table->string('name');
                $table->string('slug')->unique();
                $table->text('description')->nullable();
                $table->string('icon', 50)->nullable(); // FontAwesome or Lucide icon class
                $table->string('color', 7)->default('#3B82F6'); // Hex color
                $table->integer('sort_order')->default(0);
                $table->integer('article_count')->default(0);
                $table->boolean('is_featured')->default(false);
                $table->enum('status', ['active', 'inactive'])->default('active');
                $table->timestamps();
                
                $table->index(['status', 'sort_order']);
                $table->index(['slug']);
            });
            
            // Create knowledge base articles table
            Capsule::schema()->create('mod_kb_articles', function ($table) {
                $table->increments('id');
                $table->string('title');
                $table->string('slug')->unique();
                $table->longText('content');
                $table->text('excerpt')->nullable();
                $table->integer('category_id')->unsigned();
                $table->string('meta_title')->nullable();
                $table->text('meta_description')->nullable();
                $table->text('keywords')->nullable();
                $table->json('tags')->nullable();
                $table->string('author_name');
                $table->string('author_email');
                $table->enum('difficulty', ['beginner', 'intermediate', 'advanced'])->default('beginner');
                $table->enum('status', ['draft', 'published', 'archived'])->default('draft');
                $table->integer('view_count')->default(0);
                $table->integer('helpful_votes')->default(0);
                $table->integer('not_helpful_votes')->default(0);
                $table->boolean('is_featured')->default(false);
                $table->boolean('is_popular')->default(false);
                $table->integer('estimated_read_time')->nullable(); // in minutes
                $table->timestamp('published_at')->nullable();
                $table->timestamps();
                
                // Foreign key and indexes
                $table->foreign('category_id')->references('id')->on('mod_kb_categories')->onDelete('cascade');
                $table->index(['slug']);
                $table->index(['status', 'published_at']);
                $table->index(['category_id', 'status']);
                $table->index(['is_featured']);
                $table->index(['is_popular']);
                $table->fullText(['title', 'content', 'excerpt']); // Full-text search
            });
            
            // Create knowledge base votes table for tracking helpfulness
            Capsule::schema()->create('mod_kb_votes', function ($table) {
                $table->increments('id');
                $table->integer('article_id')->unsigned();
                $table->string('ip_address', 45);
                $table->enum('vote_type', ['helpful', 'not_helpful']);
                $table->string('user_agent')->nullable();
                $table->timestamps();
                
                $table->foreign('article_id')->references('id')->on('mod_kb_articles')->onDelete('cascade');
                $table->unique(['article_id', 'ip_address']); // Prevent duplicate votes from same IP
            });
            
            // Create knowledge base search queries table for analytics
            Capsule::schema()->create('mod_kb_search_queries', function ($table) {
                $table->increments('id');
                $table->string('query');
                $table->integer('results_count');
                $table->string('ip_address', 45);
                $table->integer('clicked_article_id')->unsigned()->nullable();
                $table->timestamps();
                
                $table->index(['query']);
                $table->index(['created_at']);
            });
            
            // Insert default categories
            $this->insertDefaultCategories();
            
            return ['success' => true, 'message' => 'Knowledge Base module installed successfully'];
            
        } catch (Exception $e) {
            return ['success' => false, 'message' => 'Installation failed: ' . $e->getMessage()];
        }
    }
    
    /**
     * Remove knowledge base tables from WHMCS database
     */
    public function uninstallModule() {
        try {
            Capsule::schema()->dropIfExists('mod_kb_search_queries');
            Capsule::schema()->dropIfExists('mod_kb_votes');
            Capsule::schema()->dropIfExists('mod_kb_articles');
            Capsule::schema()->dropIfExists('mod_kb_categories');
            
            return ['success' => true, 'message' => 'Knowledge Base module uninstalled successfully'];
            
        } catch (Exception $e) {
            return ['success' => false, 'message' => 'Uninstallation failed: ' . $e->getMessage()];
        }
    }
    
    /**
     * Create a new knowledge base article
     */
    public function createArticle($data) {
        try {
            // Generate slug if not provided
            if (empty($data['slug'])) {
                $data['slug'] = $this->generateSlug($data['title']);
            }
            
            // Auto-generate excerpt if not provided
            if (empty($data['excerpt'])) {
                $data['excerpt'] = $this->generateExcerpt($data['content']);
            }
            
            // Calculate estimated reading time
            $wordCount = str_word_count(strip_tags($data['content']));
            $readTime = max(1, ceil($wordCount / 200)); // Average reading speed 200 words/min
            
            // Prepare published_at timestamp
            $publishedAt = null;
            if ($data['status'] === 'published') {
                $publishedAt = $data['published_at'] ?? now();
            }
            
            $articleId = Capsule::table('mod_kb_articles')->insertGetId([
                'title' => $data['title'],
                'slug' => $data['slug'],
                'content' => $data['content'],
                'excerpt' => $data['excerpt'],
                'category_id' => $data['category_id'],
                'meta_title' => $data['meta_title'] ?? $data['title'],
                'meta_description' => $data['meta_description'] ?? $data['excerpt'],
                'keywords' => $data['keywords'] ?? null,
                'tags' => json_encode($data['tags'] ?? []),
                'author_name' => $data['author_name'],
                'author_email' => $data['author_email'],
                'difficulty' => $data['difficulty'] ?? 'beginner',
                'status' => $data['status'] ?? 'draft',
                'is_featured' => $data['is_featured'] ?? false,
                'estimated_read_time' => $readTime,
                'published_at' => $publishedAt,
                'created_at' => now(),
                'updated_at' => now()
            ]);
            
            // Update category article count
            $this->updateCategoryCount($data['category_id']);
            
            return ['success' => true, 'article_id' => $articleId, 'message' => 'Knowledge base article created successfully'];
            
        } catch (Exception $e) {
            return ['success' => false, 'message' => 'Failed to create knowledge base article: ' . $e->getMessage()];
        }
    }
    
    /**
     * Get knowledge base articles with filtering and search
     */
    public function getArticles($filters = []) {
        try {
            $query = Capsule::table('mod_kb_articles as a')
                           ->leftJoin('mod_kb_categories as c', 'a.category_id', '=', 'c.id')
                           ->select([
                               'a.*',
                               'c.name as category_name',
                               'c.slug as category_slug',
                               'c.color as category_color',
                               'c.icon as category_icon'
                           ]);
            
            // Apply filters
            if (!empty($filters['status'])) {
                $query->where('a.status', $filters['status']);
            }
            
            if (!empty($filters['category_id'])) {
                $query->where('a.category_id', $filters['category_id']);
            }
            
            if (!empty($filters['category_slug'])) {
                $query->where('c.slug', $filters['category_slug']);
            }
            
            if (!empty($filters['featured'])) {
                $query->where('a.is_featured', true);
            }
            
            if (!empty($filters['popular'])) {
                $query->where('a.is_popular', true);
            }
            
            if (!empty($filters['search'])) {
                $searchTerm = $filters['search'];
                $query->where(function($q) use ($searchTerm) {
                    $q->whereRaw('MATCH(a.title, a.content, a.excerpt) AGAINST (? IN NATURAL LANGUAGE MODE)', [$searchTerm])
                      ->orWhere('a.title', 'LIKE', "%{$searchTerm}%")
                      ->orWhere('a.excerpt', 'LIKE', "%{$searchTerm}%");
                });
            }
            
            if (!empty($filters['limit'])) {
                $query->limit($filters['limit']);
            }
            
            // Default ordering
            if (!empty($filters['search'])) {
                // Search results ordered by relevance
                $query->orderByRaw('MATCH(a.title, a.content, a.excerpt) AGAINST (? IN NATURAL LANGUAGE MODE) DESC', [$filters['search']])
                      ->orderBy('a.view_count', 'desc');
            } else {
                // Regular ordering
                $query->orderBy('a.is_featured', 'desc')
                      ->orderBy('a.published_at', 'desc')
                      ->orderBy('a.created_at', 'desc');
            }
            
            $articles = $query->get();
            
            // Decode JSON tags for each article
            foreach ($articles as $article) {
                $article->tags = json_decode($article->tags, true) ?? [];
                $article->helpfulness_ratio = $this->calculateHelpfulnessRatio($article->helpful_votes, $article->not_helpful_votes);
            }
            
            return ['success' => true, 'articles' => $articles];
            
        } catch (Exception $e) {
            return ['success' => false, 'message' => 'Failed to fetch knowledge base articles: ' . $e->getMessage()];
        }
    }
    
    /**
     * Get single knowledge base article by slug
     */
    public function getArticleBySlug($slug) {
        try {
            $article = Capsule::table('mod_kb_articles as a')
                             ->leftJoin('mod_kb_categories as c', 'a.category_id', '=', 'c.id')
                             ->select([
                                 'a.*',
                                 'c.name as category_name',
                                 'c.slug as category_slug',
                                 'c.color as category_color',
                                 'c.icon as category_icon'
                             ])
                             ->where('a.slug', $slug)
                             ->where('a.status', 'published')
                             ->first();
            
            if (!$article) {
                return ['success' => false, 'message' => 'Knowledge base article not found'];
            }
            
            $article->tags = json_decode($article->tags, true) ?? [];
            $article->helpfulness_ratio = $this->calculateHelpfulnessRatio($article->helpful_votes, $article->not_helpful_votes);
            
            // Increment view count
            Capsule::table('mod_kb_articles')
                   ->where('id', $article->id)
                   ->increment('view_count');
            
            return ['success' => true, 'article' => $article];
            
        } catch (Exception $e) {
            return ['success' => false, 'message' => 'Failed to fetch knowledge base article: ' . $e->getMessage()];
        }
    }
    
    /**
     * Get knowledge base categories
     */
    public function getCategories($filters = []) {
        try {
            $query = Capsule::table('mod_kb_categories');
            
            if (!empty($filters['status'])) {
                $query->where('status', $filters['status']);
            }
            
            if (!empty($filters['featured'])) {
                $query->where('is_featured', true);
            }
            
            $categories = $query->orderBy('sort_order')
                               ->orderBy('name')
                               ->get();
            
            return ['success' => true, 'categories' => $categories];
            
        } catch (Exception $e) {
            return ['success' => false, 'message' => 'Failed to fetch categories: ' . $e->getMessage()];
        }
    }
    
    /**
     * Vote on article helpfulness
     */
    public function voteOnArticle($articleId, $voteType, $ipAddress) {
        try {
            // Check if user has already voted
            $existingVote = Capsule::table('mod_kb_votes')
                                  ->where('article_id', $articleId)
                                  ->where('ip_address', $ipAddress)
                                  ->first();
            
            if ($existingVote) {
                return ['success' => false, 'message' => 'You have already voted on this article'];
            }
            
            // Record the vote
            Capsule::table('mod_kb_votes')->insert([
                'article_id' => $articleId,
                'ip_address' => $ipAddress,
                'vote_type' => $voteType,
                'user_agent' => $_SERVER['HTTP_USER_AGENT'] ?? null,
                'created_at' => now(),
                'updated_at' => now()
            ]);
            
            // Update article vote counts
            if ($voteType === 'helpful') {
                Capsule::table('mod_kb_articles')
                       ->where('id', $articleId)
                       ->increment('helpful_votes');
            } else {
                Capsule::table('mod_kb_articles')
                       ->where('id', $articleId)
                       ->increment('not_helpful_votes');
            }
            
            return ['success' => true, 'message' => 'Thank you for your feedback!'];
            
        } catch (Exception $e) {
            return ['success' => false, 'message' => 'Failed to record vote: ' . $e->getMessage()];
        }
    }
    
    /**
     * Record search query for analytics
     */
    public function recordSearchQuery($query, $resultsCount, $ipAddress, $clickedArticleId = null) {
        try {
            Capsule::table('mod_kb_search_queries')->insert([
                'query' => $query,
                'results_count' => $resultsCount,
                'ip_address' => $ipAddress,
                'clicked_article_id' => $clickedArticleId,
                'created_at' => now(),
                'updated_at' => now()
            ]);
            
            return ['success' => true];
            
        } catch (Exception $e) {
            return ['success' => false, 'message' => 'Failed to record search query: ' . $e->getMessage()];
        }
    }
    
    /**
     * Get sitemap data for SEO
     */
    public function getSitemapData() {
        try {
            $articles = Capsule::table('mod_kb_articles')
                              ->select(['slug', 'updated_at', 'published_at'])
                              ->where('status', 'published')
                              ->orderBy('published_at', 'desc')
                              ->get();
            
            $categories = Capsule::table('mod_kb_categories')
                                ->select(['slug', 'updated_at'])
                                ->where('status', 'active')
                                ->get();
            
            return ['success' => true, 'articles' => $articles, 'categories' => $categories];
            
        } catch (Exception $e) {
            return ['success' => false, 'message' => 'Failed to generate sitemap data: ' . $e->getMessage()];
        }
    }
    
    /**
     * Helper functions
     */
    private function generateSlug($title) {
        $slug = strtolower(trim($title));
        $slug = preg_replace('/[^a-z0-9-]/', '-', $slug);
        $slug = preg_replace('/-+/', '-', $slug);
        $slug = trim($slug, '-');
        
        // Check if slug exists and make it unique
        $originalSlug = $slug;
        $counter = 1;
        
        while (Capsule::table('mod_kb_articles')->where('slug', $slug)->exists()) {
            $slug = $originalSlug . '-' . $counter;
            $counter++;
        }
        
        return $slug;
    }
    
    private function generateExcerpt($content, $length = 160) {
        $text = strip_tags($content);
        if (strlen($text) > $length) {
            $text = substr($text, 0, $length);
            $text = substr($text, 0, strrpos($text, ' ')) . '...';
        }
        return $text;
    }
    
    private function calculateHelpfulnessRatio($helpful, $notHelpful) {
        $total = $helpful + $notHelpful;
        if ($total === 0) {
            return 0;
        }
        return round(($helpful / $total) * 100, 1);
    }
    
    private function updateCategoryCount($categoryId) {
        $count = Capsule::table('mod_kb_articles')
                       ->where('category_id', $categoryId)
                       ->where('status', 'published')
                       ->count();
        
        Capsule::table('mod_kb_categories')
               ->where('id', $categoryId)
               ->update(['article_count' => $count]);
    }
    
    private function insertDefaultCategories() {
        $categories = [
            [
                'name' => 'Getting Started',
                'slug' => 'getting-started',
                'description' => 'Essential guides for new users and initial setup procedures',
                'icon' => 'rocket',
                'color' => '#10B981',
                'sort_order' => 1,
                'is_featured' => true
            ],
            [
                'name' => 'Server Management',
                'slug' => 'server-management',
                'description' => 'Server administration, configuration, and maintenance guides',
                'icon' => 'server',
                'color' => '#3B82F6',
                'sort_order' => 2,
                'is_featured' => true
            ],
            [
                'name' => 'Control Panel',
                'slug' => 'control-panel',
                'description' => 'How to use your hosting control panel and manage your account',
                'icon' => 'settings',
                'color' => '#8B5CF6',
                'sort_order' => 3,
                'is_featured' => false
            ],
            [
                'name' => 'Troubleshooting',
                'slug' => 'troubleshooting',
                'description' => 'Common issues and their solutions',
                'icon' => 'tool',
                'color' => '#F59E0B',
                'sort_order' => 4,
                'is_featured' => true
            ],
            [
                'name' => 'Security',
                'slug' => 'security',
                'description' => 'Security best practices and protection measures',
                'icon' => 'shield',
                'color' => '#EF4444',
                'sort_order' => 5,
                'is_featured' => false
            ],
            [
                'name' => 'Performance Optimization',
                'slug' => 'performance',
                'description' => 'Tips and techniques to optimize your server performance',
                'icon' => 'zap',
                'color' => '#06B6D4',
                'sort_order' => 6,
                'is_featured' => false
            ],
            [
                'name' => 'Billing & Account',
                'slug' => 'billing-account',
                'description' => 'Account management, billing, and payment information',
                'icon' => 'credit-card',
                'color' => '#84CC16',
                'sort_order' => 7,
                'is_featured' => false
            ]
        ];
        
        foreach ($categories as $category) {
            Capsule::table('mod_kb_categories')->insert(array_merge($category, [
                'created_at' => now(),
                'updated_at' => now()
            ]));
        }
    }
}

// Initialize the knowledge base module
function init_whmcs_kb_module() {
    return new WhmcsKnowledgeBaseModule();
}
?>