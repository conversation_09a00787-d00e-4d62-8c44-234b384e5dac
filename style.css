        body {
            font-family: 'Inter', sans-serif;
            background-color: #020617; /* slate-950 */
            color: #d1d5db; /* gray-300 */
        }

        /* Google 2025 Accessibility Excellence - Skip Links */
        .skip-link {
            position: absolute;
            left: -10000px;
            top: auto;
            width: 1px;
            height: 1px;
            overflow: hidden;
            background: #2563eb;
            color: white;
            padding: 0.5rem 1rem;
            border-radius: 0.375rem;
            text-decoration: none;
            font-weight: 600;
            z-index: 1000;
            transition: all 0.2s ease;
        }

        .skip-link:focus {
            position: fixed;
            left: 1rem;
            top: 1rem;
            width: auto;
            height: auto;
            clip: auto;
            overflow: visible;
            box-shadow: 0 10px 25px rgba(37, 99, 235, 0.3);
        }

        /* High Contrast Mode Support */
        @media (prefers-contrast: high) {
            .skip-link {
                background: #000000;
                border: 2px solid #ffffff;
            }
        }

        /* Reduced Motion Support */
        @media (prefers-reduced-motion: reduce) {
            .skip-link {
                transition: none;
            }
        }

        /* Mobile Navigation Improvements */
        .hamburger-line {
            transform-origin: center;
        }

        /* Tablet and Mobile Responsive Fixes */
        @media (max-width: 1023px) {
            /* Ensure mobile menu button is visible on tablet */
            #mobile-menu-button {
                display: flex !important;
            }

            /* Hide desktop navigation on tablet and mobile */
            nav.lg\\:flex {
                display: none !important;
            }

            /* Adjust header padding for smaller screens */
            .container {
                padding-left: 1rem;
                padding-right: 1rem;
            }
        }

        @media (max-width: 768px) {
            /* Mobile-specific adjustments */
            .h-20 {
                height: 4rem; /* Reduce header height on mobile */
            }

            /* Ensure text doesn't overlap */
            .text-2xl {
                font-size: 1.25rem;
            }
        }

        /* Mobile Menu Animation */
        #mobile-menu {
            transition: all 0.3s ease-in-out;
        }

        /* Fix mobile menu z-index */
        header {
            z-index: 9999 !important;
        }

        #mobile-menu {
            z-index: 9998 !important;
        }

        /* Remove any gap between header and main content */
        main {
            margin-top: 0 !important;
            padding-top: 0 !important;
        }
        .hero-gradient {
            background: radial-gradient(ellipse at top, rgba(14, 165, 233, 0.15), transparent 60%);
        }
        .feature-card {
            border: 1px solid rgba(255, 255, 255, 0.1);
            background: rgba(15, 23, 42, 0.5); /* slate-900 with opacity */
            backdrop-filter: blur(10px);
            transition: all 0.3s ease;
        }
        .feature-card:hover {
            border: 1px solid rgba(56, 189, 248, 0.5); /* sky-400 with opacity */
            transform: translateY(-5px);
            box-shadow: 0 10px 25px rgba(14, 165, 233, 0.1);
        }
        .pricing-card {
            border: 1px solid rgba(255, 255, 255, 0.1);
            background: #0f172a; /* slate-900 */
            transition: all 0.3s ease;
            display: flex;
            flex-direction: column;
        }
        .pricing-card:hover {
            border-color: #38bdf8;
        }
        .pricing-card .card-content {
            flex-grow: 1;
        }
        .btn-primary {
            background-color: #0ea5e9; /* sky-500 */
            color: white;
            transition: background-color 0.3s ease;
        }
        .btn-primary:hover {
            background-color: #38bdf8; /* sky-400 */
        }
        .btn-secondary {
            background-color: transparent;
            border: 1px solid #38bdf8;
            color: #38bdf8;
            transition: all 0.3s ease;
        }
        .btn-secondary:hover {
            background-color: #38bdf8;
            color: white;
        }
        .faq-item {
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
        }
        .faq-item summary {
            cursor: pointer;
            outline: none;
        }
        .faq-item[open] summary {
           color: #38bdf8;
        }

/* --- Local utility helpers to match original look (no external CDN) --- */
.container { max-width: 1200px; margin: 0 auto; padding: 0 1rem; }
.max-w-6xl { max-width: 72rem; }
.mx-auto { margin-left: auto; margin-right: auto; }
.text-white { color: #ffffff; }
.text-gray-300 { color: #d1d5db; }
.text-blue-400 { color: #60a5fa; }
.text-purple-400 { color: #c084fc; }
.bg-slate-950 { background-color: #020617; }
.bg-slate-900 { background-color: #0f172a; }
/* Gradients */
.bg-gradient-to-br { background-image: linear-gradient(to bottom right, var(--tw-gradient-from), var(--tw-gradient-to)); }
.bg-gradient-to-r { background-image: linear-gradient(to right, var(--tw-gradient-from), var(--tw-gradient-to)); }
.bg-gradient-radial { background-image: radial-gradient(ellipse at center, var(--tw-gradient-from), var(--tw-gradient-to)); }
.from-slate-950 { --tw-gradient-from: rgba(2,6,23,1); }
.via-slate-900 { background-image: linear-gradient(to bottom right, rgba(2,6,23,1), rgba(15,23,42,1), rgba(2,6,23,1)); }
.to-slate-950 { --tw-gradient-to: rgba(2,6,23,1); }
.from-blue-500 { --tw-gradient-from: #3b82f6; }
.to-purple-500 { --tw-gradient-to: #a855f7; }
.from-emerald-500 { --tw-gradient-from: #10b981; }
.to-teal-500 { --tw-gradient-to: #14b8a6; }
.from-pink-500 { --tw-gradient-from: #ec4899; }
.to-red-500 { --tw-gradient-to: #ef4444; }
/* Rounding and shadow */
.rounded-full { border-radius: 9999px; }
.rounded { border-radius: .25rem; }
.rounded-lg { border-radius: .5rem; }
.shadow-lg { box-shadow: 0 10px 15px rgba(0,0,0,0.25); }
/* Buttons */
.btn-primary { background: linear-gradient(135deg, #0ea5e9, #3b82f6); color: #fff; padding: .75rem 1.5rem; border-radius: .5rem; font-weight: 600; text-decoration: none; display: inline-flex; align-items: center; justify-content: center; }
.btn-secondary { background: rgba(51, 65, 85, 0.8); color: #e2e8f0; border: 1px solid rgba(148, 163, 184, 0.3); padding: .75rem 1.5rem; border-radius: .5rem; font-weight: 600; text-decoration: none; display: inline-flex; align-items: center; justify-content: center; }
/* Simple animations */
@keyframes pulse { 0%, 100% { opacity: 1 } 50% { opacity: .6 } }
.animate-pulse { animation: pulse 2s ease-in-out infinite; }
@keyframes bounce { 0%, 100% { transform: translateY(0) } 50% { transform: translateY(-6px) } }
.animate-bounce { animation: bounce 2s ease-in-out infinite; }


/* --- Homepage hero utilities (extended) --- */
:root {
  --tw-gradient-from: transparent;
  --tw-gradient-via1: initial;
  --tw-gradient-via2: initial;
  --tw-gradient-to: transparent;
}
.relative { position: relative; }
.overflow-hidden { overflow: hidden; }
.text-center { text-align: center; }
.mx-auto { margin-left: auto; margin-right: auto; }
.pt-20 { padding-top: 5rem; }
.pb-32 { padding-bottom: 8rem; }
.-mt-8 { margin-top: -2rem; }
.px-4 { padding-left: 1rem; padding-right: 1rem; }
/* Responsive paddings used by container */
@media (min-width: 640px) { .sm\:px-6 { padding-left: 1.5rem; padding-right: 1.5rem; } }
@media (min-width: 1024px) { .lg\:px-8 { padding-left: 2rem; padding-right: 2rem; } }

/* Typography scale used in hero */
.text-5xl { font-size: 3rem; line-height: 1; }
.font-black { font-weight: 900; }
.leading-tight { line-height: 1.1; }
.mb-6 { margin-bottom: 1.5rem; }
@media (min-width: 768px) { .md\:text-7xl { font-size: 4.5rem; line-height: 1; } }
@media (min-width: 1024px) { .lg\:text-8xl { font-size: 6rem; line-height: 1; } }

/* Gradient backgrounds with via-stops */
.bg-gradient-to-r { background-image: linear-gradient(to right, var(--tw-gradient-from, transparent), var(--tw-gradient-via1, var(--tw-gradient-to, transparent)), var(--tw-gradient-via2, var(--tw-gradient-to, transparent)), var(--tw-gradient-to, transparent)); }
.bg-gradient-to-br { background-image: linear-gradient(to bottom right, var(--tw-gradient-from, transparent), var(--tw-gradient-via1, var(--tw-gradient-to, transparent)), var(--tw-gradient-via2, var(--tw-gradient-to, transparent)), var(--tw-gradient-to, transparent)); }
.bg-gradient-radial { background-image: radial-gradient(ellipse at center, var(--tw-gradient-from, transparent), var(--tw-gradient-to, transparent)); }

/* Color stops used in the screenshot */
.from-blue-400 { --tw-gradient-from: #60a5fa; }
.via-purple-500 { --tw-gradient-via1: #a855f7; }
.via-pink-500 { --tw-gradient-via2: #ec4899; }
.to-orange-500 { --tw-gradient-to: #f97316; }
.from-slate-950 { --tw-gradient-from: #020617; }
.via-slate-900 { --tw-gradient-via1: #0f172a; }
.to-slate-950 { --tw-gradient-to: #020617; }

/* Gradient text utilities */
.bg-clip-text { -webkit-background-clip: text; background-clip: text; }
.text-transparent { color: transparent; }

/* Badge pills and spacing */
.flex { display: flex; }
.flex-wrap { flex-wrap: wrap; }
.items-center { align-items: center; }
.justify-center { justify-content: center; }
.gap-4 { gap: 1rem; }
.mb-8 { margin-bottom: 2rem; }
.px-6 { padding-left: 1.5rem; padding-right: 1.5rem; }
.py-3 { padding-top: .75rem; padding-bottom: .75rem; }
.rounded-full { border-radius: 9999px; }
.text-sm { font-size: .875rem; line-height: 1.25rem; }
.font-bold { font-weight: 700; }
.shadow-lg { box-shadow: 0 10px 15px rgba(0,0,0,.25); }

/* Container max width utility used in hero */
.container { max-width: 1200px; margin: 0 auto; }
.max-w-6xl { max-width: 72rem; }

/* Color helpers */
.text-white { color: #ffffff; }
.text-gray-300 { color: #d1d5db; }
