<?php
/**
 * SEO-Optimized Blog Display System
 * Dynamic blog frontend that pulls from WHMCS database
 */

require_once 'whmcs-blog-module.php';

// Initialize blog module
$blogModule = init_whmcs_blog_module();

// Get URL parameters
$requestUri = $_SERVER['REQUEST_URI'];
$urlPath = parse_url($requestUri, PHP_URL_PATH);
$pathSegments = explode('/', trim($urlPath, '/'));

// Determine view type
$viewType = 'index'; // default to blog index
$postSlug = null;
$category = null;
$page = 1;

if (count($pathSegments) >= 2 && $pathSegments[0] === 'blog') {
    if (!empty($pathSegments[1])) {
        // Check if it's a category or post slug
        if (in_array($pathSegments[1], ['server-administration', 'network-optimization', 'security-compliance', 'case-studies'])) {
            $viewType = 'category';
            $category = $pathSegments[1];
        } else {
            $viewType = 'post';
            $postSlug = $pathSegments[1];
        }
    }
}

// Handle pagination
if (isset($_GET['page']) && is_numeric($_GET['page'])) {
    $page = max(1, intval($_GET['page']));
}

// Get data based on view type
$pageData = [];
$posts = [];
$currentPost = null;

switch ($viewType) {
    case 'post':
        $result = $blogModule->getBlogPostBySlug($postSlug);
        if ($result['success']) {
            $currentPost = $result['post'];
            $pageData = [
                'title' => $currentPost->meta_title ?: $currentPost->title,
                'description' => $currentPost->meta_description ?: $currentPost->excerpt,
                'keywords' => $currentPost->keywords,
                'canonical' => "https://x-zoneservers.com/blog/{$currentPost->slug}/",
                'type' => 'article'
            ];
        } else {
            header('HTTP/1.0 404 Not Found');
            $viewType = '404';
        }
        break;
        
    case 'category':
        $categoryNames = [
            'server-administration' => 'Server Administration',
            'network-optimization' => 'Network Optimization', 
            'security-compliance' => 'Security & Compliance',
            'case-studies' => 'Case Studies'
        ];
        
        $result = $blogModule->getBlogPosts([
            'status' => 'published',
            'category' => $category,
            'limit' => 20
        ]);
        
        if ($result['success']) {
            $posts = $result['posts'];
            $categoryName = $categoryNames[$category] ?? ucwords(str_replace('-', ' ', $category));
            $pageData = [
                'title' => "{$categoryName} - X-ZoneServers Blog",
                'description' => "Expert insights and tutorials about {$categoryName} from X-ZoneServers technical team.",
                'canonical' => "https://x-zoneservers.com/blog/{$category}/",
                'type' => 'website'
            ];
        }
        break;
        
    default: // index
        $result = $blogModule->getBlogPosts([
            'status' => 'published',
            'limit' => 20
        ]);
        
        if ($result['success']) {
            $posts = $result['posts'];
        }
        
        $pageData = [
            'title' => 'Hosting Blog & Technical Guides - X-ZoneServers',
            'description' => 'Expert hosting insights, technical tutorials, and industry analysis from X-ZoneServers.',
            'canonical' => 'https://x-zoneservers.com/blog/',
            'type' => 'blog'
        ];
        break;
}

// Helper function to format dates
function formatBlogDate($date) {
    return date('M j, Y', strtotime($date));
}

// Helper function to get reading time text
function getReadingTimeText($minutes) {
    return $minutes . ' min read';
}

// Helper function to get category display data
function getCategoryData($categorySlug) {
    $categories = [
        'server-administration' => ['name' => 'Server Administration', 'color' => 'blue'],
        'network-optimization' => ['name' => 'Network Optimization', 'color' => 'green'],
        'security-compliance' => ['name' => 'Security & Compliance', 'color' => 'red'],
        'case-studies' => ['name' => 'Case Studies', 'color' => 'purple']
    ];
    
    return $categories[$categorySlug] ?? ['name' => ucwords(str_replace('-', ' ', $categorySlug)), 'color' => 'gray'];
}
?>

<!DOCTYPE html>
<html lang="en" class="scroll-smooth">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo htmlspecialchars($pageData['title']); ?></title>
    <meta name="description" content="<?php echo htmlspecialchars($pageData['description']); ?>">
    <?php if (!empty($pageData['keywords'])): ?>
    <meta name="keywords" content="<?php echo htmlspecialchars($pageData['keywords']); ?>">
    <?php endif; ?>
    <meta name="author" content="X-ZoneServers">
    <meta name="robots" content="index, follow">
    <link rel="canonical" href="<?php echo htmlspecialchars($pageData['canonical']); ?>">

    <!-- Open Graph -->
    <meta property="og:type" content="<?php echo $pageData['type']; ?>">
    <meta property="og:url" content="<?php echo htmlspecialchars($pageData['canonical']); ?>">
    <meta property="og:title" content="<?php echo htmlspecialchars($pageData['title']); ?>">
    <meta property="og:description" content="<?php echo htmlspecialchars($pageData['description']); ?>">
    <meta property="og:site_name" content="X-ZoneServers">
    
    <?php if ($viewType === 'post' && $currentPost): ?>
    <meta property="article:published_time" content="<?php echo date('c', strtotime($currentPost->published_at)); ?>">
    <meta property="article:modified_time" content="<?php echo date('c', strtotime($currentPost->updated_at)); ?>">
    <meta property="article:author" content="<?php echo htmlspecialchars($currentPost->author_name); ?>">
    <meta property="article:section" content="<?php echo htmlspecialchars($currentPost->category); ?>">
    <?php if (!empty($currentPost->tags)): ?>
        <?php foreach ($currentPost->tags as $tag): ?>
    <meta property="article:tag" content="<?php echo htmlspecialchars($tag); ?>">
        <?php endforeach; ?>
    <?php endif; ?>
    <?php if ($currentPost->featured_image): ?>
    <meta property="og:image" content="<?php echo htmlspecialchars($currentPost->featured_image); ?>">
    <?php endif; ?>
    <?php endif; ?>

    <!-- Twitter -->
    <meta name="twitter:card" content="summary_large_image">
    <meta name="twitter:title" content="<?php echo htmlspecialchars($pageData['title']); ?>">
    <meta name="twitter:description" content="<?php echo htmlspecialchars($pageData['description']); ?>">
    
    <!-- Structured Data -->
    <?php if ($viewType === 'post' && $currentPost): ?>
    <script type="application/ld+json">
    {
        "@context": "https://schema.org",
        "@type": "BlogPosting",
        "@id": "<?php echo htmlspecialchars($pageData['canonical']); ?>",
        "headline": "<?php echo htmlspecialchars($currentPost->title); ?>",
        "description": "<?php echo htmlspecialchars($currentPost->excerpt); ?>",
        "datePublished": "<?php echo date('c', strtotime($currentPost->published_at)); ?>",
        "dateModified": "<?php echo date('c', strtotime($currentPost->updated_at)); ?>",
        "author": {
            "@type": "Person",
            "name": "<?php echo htmlspecialchars($currentPost->author_name); ?>",
            "jobTitle": "<?php echo htmlspecialchars($currentPost->author_title); ?>"
        },
        "publisher": {
            "@type": "Organization",
            "name": "X-ZoneServers",
            "logo": {
                "@type": "ImageObject",
                "url": "https://x-zoneservers.com/images/logo.png"
            }
        },
        <?php if ($currentPost->featured_image): ?>
        "image": "<?php echo htmlspecialchars($currentPost->featured_image); ?>",
        <?php endif; ?>
        "articleSection": "<?php echo htmlspecialchars($currentPost->category); ?>",
        "wordCount": <?php echo $currentPost->word_count; ?>,
        "timeRequired": "PT<?php echo $currentPost->read_time; ?>M",
        "mainEntityOfPage": {
            "@type": "WebPage",
            "@id": "<?php echo htmlspecialchars($pageData['canonical']); ?>"
        }
    }
    </script>
    <?php endif; ?>

    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="style.css">
</head>
<body class="bg-slate-950 text-white">
    <!-- Header -->
    <div id="header-placeholder"></div>

    <?php if ($viewType === 'post' && $currentPost): ?>
    <!-- Single Post View -->
    <article class="py-16">
        <div class="container mx-auto px-6">
            <!-- Post Header -->
            <header class="max-w-4xl mx-auto text-center mb-12">
                <!-- Breadcrumbs -->
                <nav class="mb-8" aria-label="Breadcrumb">
                    <ol class="flex items-center justify-center space-x-2 text-sm" itemscope itemtype="https://schema.org/BreadcrumbList">
                        <li itemprop="itemListElement" itemscope itemtype="https://schema.org/ListItem">
                            <a href="/" class="text-blue-400 hover:text-blue-300" itemprop="item">
                                <span itemprop="name">Home</span>
                            </a>
                            <meta itemprop="position" content="1" />
                        </li>
                        <li class="text-gray-400 mx-2">/</li>
                        <li itemprop="itemListElement" itemscope itemtype="https://schema.org/ListItem">
                            <a href="/blog/" class="text-blue-400 hover:text-blue-300" itemprop="item">
                                <span itemprop="name">Blog</span>
                            </a>
                            <meta itemprop="position" content="2" />
                        </li>
                        <li class="text-gray-400 mx-2">/</li>
                        <li itemprop="itemListElement" itemscope itemtype="https://schema.org/ListItem">
                            <span itemprop="name" class="text-white"><?php echo htmlspecialchars($currentPost->title); ?></span>
                            <meta itemprop="position" content="3" />
                        </li>
                    </ol>
                </nav>

                <!-- Category Badge -->
                <?php $categoryData = getCategoryData($currentPost->category); ?>
                <div class="mb-6">
                    <a href="/blog/<?php echo htmlspecialchars($currentPost->category); ?>/" 
                       class="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-<?php echo $categoryData['color']; ?>-600 text-white hover:bg-<?php echo $categoryData['color']; ?>-700">
                        <?php echo htmlspecialchars($categoryData['name']); ?>
                    </a>
                </div>

                <h1 class="text-4xl md:text-5xl font-bold mb-6 bg-gradient-to-r from-white via-blue-100 to-blue-200 bg-clip-text text-transparent">
                    <?php echo htmlspecialchars($currentPost->title); ?>
                </h1>

                <p class="text-xl text-gray-300 mb-8 leading-relaxed">
                    <?php echo htmlspecialchars($currentPost->excerpt); ?>
                </p>

                <!-- Post Meta -->
                <div class="flex flex-wrap items-center justify-center gap-6 text-sm text-gray-400">
                    <div class="flex items-center gap-2">
                        <?php if ($currentPost->author_image): ?>
                        <img src="<?php echo htmlspecialchars($currentPost->author_image); ?>" 
                             alt="<?php echo htmlspecialchars($currentPost->author_name); ?>"
                             class="w-8 h-8 rounded-full">
                        <?php endif; ?>
                        <div>
                            <div class="font-medium text-white"><?php echo htmlspecialchars($currentPost->author_name); ?></div>
                            <?php if ($currentPost->author_title): ?>
                            <div class="text-gray-500"><?php echo htmlspecialchars($currentPost->author_title); ?></div>
                            <?php endif; ?>
                        </div>
                    </div>
                    <div class="flex items-center gap-2">
                        <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"></path>
                        </svg>
                        <?php echo formatBlogDate($currentPost->published_at); ?>
                    </div>
                    <div class="flex items-center gap-2">
                        <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                        </svg>
                        <?php echo getReadingTimeText($currentPost->read_time); ?>
                    </div>
                    <div class="flex items-center gap-2">
                        <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                        </svg>
                        <?php echo number_format($currentPost->word_count); ?> words
                    </div>
                </div>
            </header>

            <!-- Featured Image -->
            <?php if ($currentPost->featured_image): ?>
            <div class="max-w-4xl mx-auto mb-12">
                <img src="<?php echo htmlspecialchars($currentPost->featured_image); ?>" 
                     alt="<?php echo htmlspecialchars($currentPost->title); ?>"
                     class="w-full h-auto rounded-xl">
            </div>
            <?php endif; ?>

            <!-- Post Content -->
            <div class="max-w-4xl mx-auto">
                <div class="prose prose-lg prose-invert max-w-none">
                    <?php echo $currentPost->content; ?>
                </div>

                <!-- Tags -->
                <?php if (!empty($currentPost->tags)): ?>
                <div class="mt-12 pt-8 border-t border-slate-800">
                    <div class="flex flex-wrap gap-2">
                        <?php foreach ($currentPost->tags as $tag): ?>
                        <span class="px-3 py-1 bg-slate-800 text-gray-300 rounded-full text-sm">
                            <?php echo htmlspecialchars($tag); ?>
                        </span>
                        <?php endforeach; ?>
                    </div>
                </div>
                <?php endif; ?>
            </div>
        </div>
    </article>

    <?php elseif ($viewType === '404'): ?>
    <!-- 404 Error -->
    <div class="py-32 text-center">
        <h1 class="text-6xl font-bold text-gray-600 mb-4">404</h1>
        <h2 class="text-2xl font-semibold mb-4">Blog Post Not Found</h2>
        <p class="text-gray-400 mb-8">The blog post you're looking for doesn't exist or has been moved.</p>
        <a href="/blog/" class="bg-blue-600 hover:bg-blue-700 text-white px-6 py-3 rounded-lg">
            Back to Blog
        </a>
    </div>

    <?php else: ?>
    <!-- Blog Index/Category View -->
    <section class="py-16">
        <div class="container mx-auto px-6">
            <div class="text-center mb-12">
                <?php if ($viewType === 'category'): ?>
                <h1 class="text-4xl font-bold mb-4"><?php echo htmlspecialchars($categoryNames[$category]); ?></h1>
                <p class="text-gray-400 max-w-2xl mx-auto">
                    Expert insights and tutorials about <?php echo htmlspecialchars($categoryNames[$category]); ?>
                </p>
                <?php else: ?>
                <h1 class="text-4xl font-bold mb-4">Latest Blog Posts</h1>
                <p class="text-gray-400 max-w-2xl mx-auto">
                    Expert hosting insights, technical tutorials, and industry analysis
                </p>
                <?php endif; ?>
            </div>

            <?php if (empty($posts)): ?>
            <div class="text-center py-16">
                <svg class="mx-auto h-12 w-12 text-gray-400 mb-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                </svg>
                <h3 class="text-xl font-medium text-gray-300 mb-2">No blog posts found</h3>
                <p class="text-gray-500">Check back soon for new content!</p>
            </div>
            <?php else: ?>
            <div class="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
                <?php foreach ($posts as $post): ?>
                <?php $categoryData = getCategoryData($post->category); ?>
                <article class="bg-slate-800 rounded-xl overflow-hidden hover:bg-slate-750 transition-colors group">
                    <?php if ($post->featured_image): ?>
                    <div class="aspect-video relative overflow-hidden">
                        <img src="<?php echo htmlspecialchars($post->featured_image); ?>"
                             alt="<?php echo htmlspecialchars($post->title); ?>"
                             class="w-full h-full object-cover group-hover:scale-105 transition-transform duration-300">
                        <div class="absolute top-4 left-4">
                            <span class="bg-<?php echo $categoryData['color']; ?>-600 text-white px-3 py-1 rounded-full text-sm font-medium">
                                <?php echo htmlspecialchars($categoryData['name']); ?>
                            </span>
                        </div>
                        <div class="absolute bottom-4 right-4 text-white text-sm">
                            <?php echo formatBlogDate($post->published_at); ?>
                        </div>
                    </div>
                    <?php endif; ?>
                    
                    <div class="p-6">
                        <h2 class="text-xl font-bold mb-3 group-hover:text-blue-400 transition-colors">
                            <a href="/blog/<?php echo htmlspecialchars($post->slug); ?>/">
                                <?php echo htmlspecialchars($post->title); ?>
                            </a>
                        </h2>
                        
                        <p class="text-gray-400 mb-4 line-clamp-3">
                            <?php echo htmlspecialchars($post->excerpt); ?>
                        </p>
                        
                        <div class="flex items-center justify-between">
                            <div class="flex items-center gap-3">
                                <?php if ($post->author_image): ?>
                                <img src="<?php echo htmlspecialchars($post->author_image); ?>"
                                     alt="<?php echo htmlspecialchars($post->author_name); ?>"
                                     class="w-8 h-8 rounded-full">
                                <?php endif; ?>
                                <div class="text-sm">
                                    <div class="font-medium"><?php echo htmlspecialchars($post->author_name); ?></div>
                                    <?php if ($post->author_title): ?>
                                    <div class="text-gray-500"><?php echo htmlspecialchars($post->author_title); ?></div>
                                    <?php endif; ?>
                                </div>
                            </div>
                            
                            <div class="text-sm text-gray-500">
                                <?php echo getReadingTimeText($post->read_time); ?>
                            </div>
                        </div>
                    </div>
                </article>
                <?php endforeach; ?>
            </div>
            <?php endif; ?>
        </div>
    </section>
    <?php endif; ?>

    <!-- Footer -->
    <div id="footer-placeholder"></div>

    <!-- Scripts -->
    <script src="js/header-loader.js"></script>
    <script src="js/footer-loader.js"></script>
</body>
</html>