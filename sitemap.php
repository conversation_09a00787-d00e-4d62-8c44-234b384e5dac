<?php
/**
 * Main XML Sitemap for X-ZoneServers
 * Includes static pages and blog posts for SEO
 */

require_once 'whmcs-blog-module.php';

// Set proper headers for XML sitemap
header('Content-Type: application/xml; charset=UTF-8');

// Initialize blog module for post count
$blogModule = init_whmcs_blog_module();
$blogResult = $blogModule->getBlogPosts(['status' => 'published', 'limit' => 1]);
$hasBlogPosts = $blogResult['success'] && !empty($blogResult['posts']);

echo '<?xml version="1.0" encoding="UTF-8"?>';
?>
<sitemapindex xmlns="http://www.sitemaps.org/schemas/sitemap/0.9">
    
    <!-- Main Static Pages Sitemap -->
    <sitemap>
        <loc>https://x-zoneservers.com/static-sitemap.xml</loc>
        <lastmod><?php echo date('c'); ?></lastmod>
    </sitemap>
    
    <?php if ($hasBlogPosts): ?>
    <!-- Blog Posts Sitemap -->
    <sitemap>
        <loc>https://x-zoneservers.com/blog-sitemap.xml</loc>
        <lastmod><?php echo date('c'); ?></lastmod>
    </sitemap>
    <?php endif; ?>
    
</sitemapindex>