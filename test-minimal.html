<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Minimal Test - X-ZoneServers</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Arial, sans-serif;
            background: linear-gradient(135deg, #0f172a 0%, #1e293b 50%, #334155 100%);
            color: white;
            min-height: 100vh;
            padding: 20px;
        }
        
        .container {
            max-width: 1000px;
            margin: 0 auto;
        }
        
        .header {
            text-align: center;
            margin-bottom: 40px;
            padding: 40px 0;
        }
        
        .header h1 {
            font-size: 3rem;
            font-weight: 700;
            margin-bottom: 10px;
            background: linear-gradient(135deg, #3b82f6, #06b6d4);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }
        
        .status-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin-bottom: 40px;
        }
        
        .status-card {
            background: rgba(30, 41, 59, 0.8);
            border: 1px solid rgba(59, 130, 246, 0.3);
            border-radius: 12px;
            padding: 24px;
            backdrop-filter: blur(10px);
        }
        
        .status-success {
            border-color: rgba(34, 197, 94, 0.5);
            background: rgba(34, 197, 94, 0.1);
        }
        
        .status-warning {
            border-color: rgba(245, 158, 11, 0.5);
            background: rgba(245, 158, 11, 0.1);
        }
        
        .status-error {
            border-color: rgba(239, 68, 68, 0.5);
            background: rgba(239, 68, 68, 0.1);
        }
        
        .status-icon {
            font-size: 2rem;
            margin-bottom: 12px;
            display: block;
        }
        
        .status-title {
            font-size: 1.25rem;
            font-weight: 600;
            margin-bottom: 8px;
        }
        
        .status-desc {
            color: rgba(255, 255, 255, 0.8);
            font-size: 0.9rem;
            line-height: 1.5;
        }
        
        .test-results {
            background: rgba(15, 23, 42, 0.8);
            border-radius: 12px;
            padding: 24px;
            margin-bottom: 30px;
        }
        
        .test-item {
            display: flex;
            align-items: center;
            padding: 8px 0;
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
        }
        
        .test-item:last-child {
            border-bottom: none;
        }
        
        .test-status {
            width: 20px;
            height: 20px;
            border-radius: 50%;
            margin-right: 12px;
            flex-shrink: 0;
        }
        
        .test-success { background: #22c55e; }
        .test-error { background: #ef4444; }
        .test-warning { background: #f59e0b; }
        
        .nav-buttons {
            display: flex;
            gap: 16px;
            justify-content: center;
            flex-wrap: wrap;
        }
        
        .btn {
            padding: 12px 24px;
            border: none;
            border-radius: 8px;
            font-weight: 600;
            text-decoration: none;
            display: inline-block;
            transition: all 0.2s;
            cursor: pointer;
        }
        
        .btn-primary {
            background: linear-gradient(135deg, #3b82f6, #1d4ed8);
            color: white;
        }
        
        .btn-secondary {
            background: rgba(71, 85, 105, 0.8);
            color: white;
            border: 1px solid rgba(148, 163, 184, 0.3);
        }
        
        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.3);
        }
        
        .info-box {
            background: rgba(59, 130, 246, 0.1);
            border: 1px solid rgba(59, 130, 246, 0.3);
            border-radius: 8px;
            padding: 16px;
            margin: 20px 0;
        }
        
        .code {
            background: rgba(0, 0, 0, 0.5);
            padding: 2px 6px;
            border-radius: 4px;
            font-family: 'Courier New', monospace;
            font-size: 0.9rem;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🚀 X-ZoneServers</h1>
            <p>Minimal Test - No External Dependencies</p>
        </div>

        <div class="status-grid">
            <div class="status-card status-success">
                <span class="status-icon">✅</span>
                <h3 class="status-title">Page Loading</h3>
                <p class="status-desc">This page loaded successfully without external dependencies, redirects, or JavaScript errors.</p>
            </div>

            <div class="status-card status-success">
                <span class="status-icon">🎨</span>
                <h3 class="status-title">CSS Styling</h3>
                <p class="status-desc">All styling is inline CSS - no external stylesheets required. Clean, modern design.</p>
            </div>

            <div class="status-card status-success">
                <span class="status-icon">🔒</span>
                <h3 class="status-title">Security</h3>
                <p class="status-desc">No CSP violations, no cross-origin issues, no external resource dependencies.</p>
            </div>

            <div class="status-card status-warning">
                <span class="status-icon">⚠️</span>
                <h3 class="status-title">Main Site Issues</h3>
                <p class="status-desc">Your main site has missing files and external CDN issues that need to be resolved.</p>
            </div>
        </div>

        <div class="test-results">
            <h3 style="margin-bottom: 20px; font-size: 1.5rem;">🧪 Test Results</h3>
            
            <div class="test-item">
                <div class="test-status test-success"></div>
                <div>
                    <strong>HTML Loading:</strong> Perfect - no redirects or loops
                </div>
            </div>
            
            <div class="test-item">
                <div class="test-status test-success"></div>
                <div>
                    <strong>CSS Rendering:</strong> Working - inline styles applied
                </div>
            </div>
            
            <div class="test-item">
                <div class="test-status test-success"></div>
                <div>
                    <strong>JavaScript:</strong> Minimal and functional
                </div>
            </div>
            
            <div class="test-item">
                <div class="test-status test-error"></div>
                <div>
                    <strong>External CDNs:</strong> Failing with 503 errors (Cloudflare/server issue)
                </div>
            </div>
            
            <div class="test-item">
                <div class="test-status test-error"></div>
                <div>
                    <strong>Local Files:</strong> Missing <span class="code">style.css</span>, <span class="code">manifest.json</span>, images
                </div>
            </div>
            
            <div class="test-item">
                <div class="test-status test-warning"></div>
                <div>
                    <strong>JavaScript Complexity:</strong> Too many scripts causing conflicts
                </div>
            </div>
        </div>

        <div class="info-box">
            <h4 style="margin-bottom: 12px;">📋 Next Steps to Fix Your Main Site:</h4>
            <ol style="padding-left: 20px; line-height: 1.6;">
                <li><strong>Create missing files:</strong> <span class="code">style.css</span>, <span class="code">manifest.json</span></li>
                <li><strong>Fix Cloudflare settings:</strong> Check security level and caching rules</li>
                <li><strong>Simplify JavaScript:</strong> Remove complex performance monitoring scripts</li>
                <li><strong>Add missing images:</strong> Create the WebP versions or disable image optimization</li>
                <li><strong>Test incrementally:</strong> Add features back one by one</li>
            </ol>
        </div>

        <div class="nav-buttons">
            <a href="/" class="btn btn-primary">🏠 Homepage</a>
            <a href="/test-basic.html" class="btn btn-secondary">🧪 Basic Test</a>
            <a href="/test-simple.html" class="btn btn-secondary">📊 Simple Test</a>
            <a href="/dedicated" class="btn btn-secondary">💻 Dedicated Servers</a>
        </div>
    </div>

    <script>
        // Minimal JavaScript for testing
        console.log('✅ Minimal test page loaded successfully');
        console.log('🌐 Current URL:', window.location.href);
        console.log('⏰ Load time:', new Date().toLocaleTimeString());
        
        // Test basic functionality
        document.addEventListener('DOMContentLoaded', function() {
            console.log('✅ DOM loaded without errors');
            
            // Add click tracking to buttons
            document.querySelectorAll('.btn').forEach(btn => {
                btn.addEventListener('click', function(e) {
                    console.log('🔗 Button clicked:', this.textContent.trim());
                });
            });
        });
        
        // Monitor for any errors
        window.addEventListener('error', function(e) {
            console.error('❌ JavaScript error:', e.message);
        });
        
        // Simple performance check
        window.addEventListener('load', function() {
            const loadTime = performance.now();
            console.log(`⚡ Page loaded in ${loadTime.toFixed(2)}ms`);
        });
    </script>
</body>
</html>
