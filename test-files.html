<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>File Loading Test - X-ZoneServers</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            background: #1a1a1a;
            color: white;
            padding: 20px;
            margin: 0;
        }
        .container {
            max-width: 1000px;
            margin: 0 auto;
        }
        .test-section {
            background: #2a2a2a;
            border: 1px solid #444;
            border-radius: 8px;
            padding: 20px;
            margin: 20px 0;
        }
        .test-item {
            display: flex;
            align-items: center;
            padding: 10px 0;
            border-bottom: 1px solid #444;
        }
        .test-item:last-child {
            border-bottom: none;
        }
        .status {
            width: 20px;
            height: 20px;
            border-radius: 50%;
            margin-right: 15px;
            flex-shrink: 0;
        }
        .status-loading { background: #fbbf24; }
        .status-success { background: #10b981; }
        .status-error { background: #ef4444; }
        .test-url {
            font-family: monospace;
            background: #000;
            padding: 2px 6px;
            border-radius: 4px;
            margin-left: 10px;
        }
        .log {
            background: #000;
            color: #0f0;
            padding: 15px;
            border-radius: 5px;
            font-family: monospace;
            font-size: 12px;
            max-height: 300px;
            overflow-y: auto;
            margin: 15px 0;
        }
        button {
            background: #3b82f6;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background: #2563eb;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔍 File Loading Diagnostic Test</h1>
        <p>Testing which files can be loaded from your server...</p>

        <div class="test-section">
            <h2>📄 Local Files Test</h2>
            <div id="local-files-test">
                <div class="test-item">
                    <div class="status status-loading" id="status-style"></div>
                    <span>style.css</span>
                    <span class="test-url">/style.css</span>
                </div>
                <div class="test-item">
                    <div class="status status-loading" id="status-manifest"></div>
                    <span>manifest.json</span>
                    <span class="test-url">/manifest.json</span>
                </div>
                <div class="test-item">
                    <div class="status status-loading" id="status-critical"></div>
                    <span>critical.css</span>
                    <span class="test-url">/critical.css</span>
                </div>
                <div class="test-item">
                    <div class="status status-loading" id="status-sw"></div>
                    <span>sw.js</span>
                    <span class="test-url">/sw.js</span>
                </div>
            </div>
        </div>

        <div class="test-section">
            <h2>🌐 External CDN Test</h2>
            <div id="external-test">
                <div class="test-item">
                    <div class="status status-loading" id="status-tailwind"></div>
                    <span>Tailwind CSS (cdnjs)</span>
                    <span class="test-url">cdnjs.cloudflare.com</span>
                </div>
                <div class="test-item">
                    <div class="status status-loading" id="status-fonts"></div>
                    <span>Google Fonts</span>
                    <span class="test-url">fonts.googleapis.com</span>
                </div>
                <div class="test-item">
                    <div class="status status-loading" id="status-lucide"></div>
                    <span>Lucide Icons</span>
                    <span class="test-url">unpkg.com</span>
                </div>
            </div>
        </div>

        <div class="test-section">
            <h2>🖼️ Image Files Test</h2>
            <div id="images-test">
                <div class="test-item">
                    <div class="status status-loading" id="status-logo"></div>
                    <span>Logo (PNG)</span>
                    <span class="test-url">/images/logo.png</span>
                </div>
                <div class="test-item">
                    <div class="status status-loading" id="status-og"></div>
                    <span>OG Image (JPG)</span>
                    <span class="test-url">/images/og-homepage.jpg</span>
                </div>
                <div class="test-item">
                    <div class="status status-loading" id="status-hero"></div>
                    <span>Hero Background (JPG)</span>
                    <span class="test-url">/images/hero-background.jpg</span>
                </div>
            </div>
        </div>

        <div class="test-section">
            <h2>📊 Test Controls</h2>
            <button onclick="runAllTests()">🔄 Run All Tests</button>
            <button onclick="clearLog()">🗑️ Clear Log</button>
            <button onclick="testServerResponse()">🔍 Test Server Response</button>
        </div>

        <div class="test-section">
            <h2>📝 Test Log</h2>
            <div id="test-log" class="log">
                <div>Diagnostic test initialized...</div>
            </div>
        </div>
    </div>

    <script>
        function log(message, type = 'info') {
            const logDiv = document.getElementById('test-log');
            const timestamp = new Date().toLocaleTimeString();
            const color = type === 'error' ? '#ff6b6b' : 
                         type === 'success' ? '#51cf66' : 
                         type === 'warning' ? '#ffd43b' : '#74c0fc';
            
            logDiv.innerHTML += `<div style="color: ${color}">[${timestamp}] ${message}</div>`;
            logDiv.scrollTop = logDiv.scrollHeight;
        }

        function updateStatus(id, success, message = '') {
            const statusEl = document.getElementById(id);
            statusEl.className = `status ${success ? 'status-success' : 'status-error'}`;
            if (message) {
                log(`${id.replace('status-', '')}: ${message}`, success ? 'success' : 'error');
            }
        }

        async function testFile(url, statusId, description) {
            try {
                log(`Testing ${description}: ${url}`, 'info');
                const response = await fetch(url, { method: 'HEAD' });
                
                if (response.ok) {
                    updateStatus(statusId, true, `✅ ${description} - Status: ${response.status}`);
                    return true;
                } else {
                    updateStatus(statusId, false, `❌ ${description} - Status: ${response.status} ${response.statusText}`);
                    return false;
                }
            } catch (error) {
                updateStatus(statusId, false, `❌ ${description} - Error: ${error.message}`);
                return false;
            }
        }

        async function testLocalFiles() {
            log('🔍 Testing local files...', 'info');
            
            await testFile('/style.css', 'status-style', 'Main stylesheet');
            await testFile('/manifest.json', 'status-manifest', 'PWA manifest');
            await testFile('/critical.css', 'status-critical', 'Critical CSS');
            await testFile('/sw.js', 'status-sw', 'Service worker');
        }

        async function testExternalCDNs() {
            log('🌐 Testing external CDNs...', 'info');
            
            await testFile('https://cdnjs.cloudflare.com/ajax/libs/tailwindcss/3.4.17/tailwind.min.css', 'status-tailwind', 'Tailwind CSS');
            await testFile('https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700;900&display=swap', 'status-fonts', 'Google Fonts');
            await testFile('https://unpkg.com/lucide@latest/dist/umd/lucide.js', 'status-lucide', 'Lucide Icons');
        }

        async function testImages() {
            log('🖼️ Testing image files...', 'info');
            
            await testFile('/images/logo.png', 'status-logo', 'Logo PNG');
            await testFile('/images/og-homepage.jpg', 'status-og', 'OG Image JPG');
            await testFile('/images/hero-background.jpg', 'status-hero', 'Hero Background JPG');
        }

        async function runAllTests() {
            log('🚀 Starting comprehensive file test...', 'info');
            clearLog();
            
            await testLocalFiles();
            await testExternalCDNs();
            await testImages();
            
            log('✅ All tests completed!', 'success');
        }

        function clearLog() {
            document.getElementById('test-log').innerHTML = '<div>Log cleared...</div>';
        }

        async function testServerResponse() {
            log('🔍 Testing server response headers...', 'info');
            
            try {
                const response = await fetch(window.location.href);
                log(`Server: ${response.headers.get('server') || 'Unknown'}`, 'info');
                log(`Content-Type: ${response.headers.get('content-type') || 'Unknown'}`, 'info');
                log(`Cache-Control: ${response.headers.get('cache-control') || 'None'}`, 'info');
                log(`X-Powered-By: ${response.headers.get('x-powered-by') || 'None'}`, 'info');
                
                // Test CORS headers
                log(`Access-Control-Allow-Origin: ${response.headers.get('access-control-allow-origin') || 'None'}`, 'info');
                log(`Cross-Origin-Resource-Policy: ${response.headers.get('cross-origin-resource-policy') || 'None'}`, 'info');
                
            } catch (error) {
                log(`❌ Server test failed: ${error.message}`, 'error');
            }
        }

        // Auto-run tests when page loads
        document.addEventListener('DOMContentLoaded', function() {
            log('📋 File diagnostic test loaded', 'success');
            log(`🌐 Testing from: ${window.location.origin}`, 'info');
            
            // Run tests after a short delay
            setTimeout(runAllTests, 1000);
        });

        // Monitor for errors
        window.addEventListener('error', function(e) {
            log(`❌ JavaScript Error: ${e.message}`, 'error');
        });
    </script>
</body>
</html>
