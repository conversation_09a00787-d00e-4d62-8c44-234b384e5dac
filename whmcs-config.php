<?php
/**
 * WHMCS Configuration File
 * Update these settings with your WHMCS installation details
 */

// WHMCS API Configuration
return [
    // WHMCS Installation URL (without trailing slash)
    'whmcs_url' => 'https://your-whmcs-domain.com/includes/api.php',
    
    // WHMCS Admin Credentials
    'whmcs_username' => 'your-admin-username',
    'whmcs_password' => 'your-api-password', // Use API authentication token for better security
    
    // Support Department IDs (Update these with your actual WHMCS department IDs)
    // You can find these in your WHMCS Admin Area under Support > Support Departments
    'department_mapping' => [
        'sales' => 1,           // Sales Department ID
        'technical' => 2,       // Technical Support Department ID
        'billing' => 3,         // Billing Department ID
        'partnership' => 1,     // Partnership inquiries (usually goes to sales)
        'other' => 2            // General inquiries (usually goes to technical support)
    ],
    
    // Default Settings
    'default_priority' => 'Medium', // Low, Medium, High
    'enable_logging' => true,       // Log ticket creation attempts
    'debug_mode' => false,          // Set to true for debugging (disable in production)
];
?>