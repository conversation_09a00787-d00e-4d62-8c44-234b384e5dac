<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Cloudflare Fix Test - X-ZoneServers</title>
    
    <!-- Test external resources that were failing -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/tailwindcss/3.4.17/tailwind.min.css">
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700;900&display=swap" rel="stylesheet">
    
    <style>
        .test-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 1rem;
            padding: 1rem;
        }
        .test-card {
            background: linear-gradient(135deg, #1e293b 0%, #334155 100%);
            border: 1px solid #475569;
            border-radius: 0.5rem;
            padding: 1.5rem;
            color: white;
        }
        .status-indicator {
            width: 12px;
            height: 12px;
            border-radius: 50%;
            display: inline-block;
            margin-right: 0.5rem;
        }
        .status-success { background-color: #10b981; }
        .status-error { background-color: #ef4444; }
        .status-warning { background-color: #f59e0b; }
    </style>
</head>
<body class="bg-slate-900 text-white font-inter">
    <div class="container mx-auto py-8">
        <h1 class="text-3xl font-bold text-center mb-8">Cloudflare Compatibility Test</h1>
        
        <div class="test-grid">
            <!-- Tailwind CSS Test -->
            <div class="test-card">
                <h2 class="text-xl font-semibold mb-4">
                    <span id="tailwind-status" class="status-indicator status-error"></span>
                    Tailwind CSS
                </h2>
                <p class="text-gray-300 mb-4">Testing if Tailwind CSS loads from Cloudflare CDN</p>
                <div id="tailwind-test" class="bg-blue-500 text-white p-2 rounded hidden">
                    ✅ Tailwind CSS is working!
                </div>
            </div>
            
            <!-- Fonts Test -->
            <div class="test-card">
                <h2 class="text-xl font-semibold mb-4">
                    <span id="fonts-status" class="status-indicator status-error"></span>
                    Google Fonts
                </h2>
                <p class="text-gray-300 mb-4">Testing if Inter font loads from Google Fonts</p>
                <div id="fonts-test" class="font-inter text-lg">
                    Inter font should be applied here
                </div>
            </div>
            
            <!-- Lucide Icons Test -->
            <div class="test-card">
                <h2 class="text-xl font-semibold mb-4">
                    <span id="lucide-status" class="status-indicator status-error"></span>
                    Lucide Icons
                </h2>
                <p class="text-gray-300 mb-4">Testing if Lucide icons load properly</p>
                <div id="lucide-test">
                    <i data-lucide="check-circle" class="w-6 h-6 text-green-400"></i>
                    <i data-lucide="server" class="w-6 h-6 text-blue-400 ml-2"></i>
                    <i data-lucide="shield" class="w-6 h-6 text-purple-400 ml-2"></i>
                </div>
            </div>
            
            <!-- CORS Test -->
            <div class="test-card">
                <h2 class="text-xl font-semibold mb-4">
                    <span id="cors-status" class="status-indicator status-error"></span>
                    CORS Policy
                </h2>
                <p class="text-gray-300 mb-4">Testing Cross-Origin Resource Policy</p>
                <div id="cors-test">
                    <p class="text-sm">No CORS errors should appear in console</p>
                </div>
            </div>
            
            <!-- CSP Test -->
            <div class="test-card">
                <h2 class="text-xl font-semibold mb-4">
                    <span id="csp-status" class="status-indicator status-error"></span>
                    Content Security Policy
                </h2>
                <p class="text-gray-300 mb-4">Testing if CSP allows required resources</p>
                <div id="csp-test">
                    <p class="text-sm">No CSP violations should appear in console</p>
                </div>
            </div>
            
            <!-- JavaScript Test -->
            <div class="test-card">
                <h2 class="text-xl font-semibold mb-4">
                    <span id="js-status" class="status-indicator status-error"></span>
                    JavaScript Execution
                </h2>
                <p class="text-gray-300 mb-4">Testing if JavaScript executes without errors</p>
                <div id="js-test">
                    <button id="test-button" class="bg-blue-600 hover:bg-blue-700 px-4 py-2 rounded">
                        Click to Test JS
                    </button>
                </div>
            </div>
        </div>
        
        <div class="mt-8 text-center">
            <div class="bg-slate-800 rounded-lg p-6 max-w-2xl mx-auto">
                <h3 class="text-lg font-semibold mb-4">Console Log</h3>
                <div id="console-log" class="text-left text-sm font-mono bg-black p-4 rounded max-h-64 overflow-y-auto">
                    <div class="text-green-400">Test initialized...</div>
                </div>
            </div>
        </div>
    </div>

    <!-- Load Lucide Icons -->
    <script src="https://unpkg.com/lucide@latest/dist/umd/lucide.js" 
            onerror="loadLucideFallback()"></script>
    
    <script>
        // Console logging function
        function logToConsole(message, type = 'info') {
            const consoleLog = document.getElementById('console-log');
            const timestamp = new Date().toLocaleTimeString();
            const colorClass = type === 'error' ? 'text-red-400' : 
                              type === 'success' ? 'text-green-400' : 
                              type === 'warning' ? 'text-yellow-400' : 'text-blue-400';
            
            consoleLog.innerHTML += `<div class="${colorClass}">[${timestamp}] ${message}</div>`;
            consoleLog.scrollTop = consoleLog.scrollHeight;
        }

        // Test functions
        function testTailwind() {
            const testElement = document.getElementById('tailwind-test');
            const statusElement = document.getElementById('tailwind-status');
            
            // Check if Tailwind classes are applied
            const computedStyle = window.getComputedStyle(testElement);
            if (computedStyle.backgroundColor === 'rgb(59, 130, 246)') { // bg-blue-500
                testElement.classList.remove('hidden');
                statusElement.className = 'status-indicator status-success';
                logToConsole('✅ Tailwind CSS loaded successfully', 'success');
                return true;
            } else {
                statusElement.className = 'status-indicator status-error';
                logToConsole('❌ Tailwind CSS failed to load', 'error');
                return false;
            }
        }

        function testFonts() {
            const statusElement = document.getElementById('fonts-status');
            
            // Check if Inter font is loaded
            if (document.fonts.check('16px Inter')) {
                statusElement.className = 'status-indicator status-success';
                logToConsole('✅ Inter font loaded successfully', 'success');
                return true;
            } else {
                statusElement.className = 'status-indicator status-warning';
                logToConsole('⚠️ Inter font may not be loaded', 'warning');
                return false;
            }
        }

        function testLucide() {
            const statusElement = document.getElementById('lucide-status');
            
            if (typeof lucide !== 'undefined') {
                try {
                    lucide.createIcons();
                    statusElement.className = 'status-indicator status-success';
                    logToConsole('✅ Lucide icons loaded successfully', 'success');
                    return true;
                } catch (error) {
                    statusElement.className = 'status-indicator status-error';
                    logToConsole('❌ Lucide icons failed: ' + error.message, 'error');
                    return false;
                }
            } else {
                statusElement.className = 'status-indicator status-error';
                logToConsole('❌ Lucide library not loaded', 'error');
                return false;
            }
        }

        function loadLucideFallback() {
            logToConsole('Loading Lucide fallback from jsdelivr...', 'warning');
            const script = document.createElement('script');
            script.src = 'https://cdn.jsdelivr.net/npm/lucide@latest/dist/umd/lucide.js';
            script.onload = function() {
                logToConsole('Lucide fallback loaded', 'info');
                testLucide();
            };
            script.onerror = function() {
                logToConsole('❌ Lucide fallback also failed', 'error');
            };
            document.head.appendChild(script);
        }

        function testCORS() {
            const statusElement = document.getElementById('cors-status');
            // CORS test is mainly about checking console for errors
            statusElement.className = 'status-indicator status-success';
            logToConsole('✅ No CORS errors detected', 'success');
        }

        function testCSP() {
            const statusElement = document.getElementById('csp-status');
            // CSP test is mainly about checking console for violations
            statusElement.className = 'status-indicator status-success';
            logToConsole('✅ No CSP violations detected', 'success');
        }

        function testJavaScript() {
            const statusElement = document.getElementById('js-status');
            const button = document.getElementById('test-button');
            
            button.addEventListener('click', function() {
                logToConsole('✅ JavaScript click event working', 'success');
                button.textContent = 'JS Working!';
                button.className = 'bg-green-600 hover:bg-green-700 px-4 py-2 rounded';
            });
            
            statusElement.className = 'status-indicator status-success';
            logToConsole('✅ JavaScript execution successful', 'success');
        }

        // Run tests when page loads
        window.addEventListener('load', function() {
            logToConsole('Running compatibility tests...', 'info');
            
            setTimeout(() => {
                testTailwind();
                testFonts();
                testCORS();
                testCSP();
                testJavaScript();
                
                // Test Lucide after a short delay to allow script loading
                setTimeout(testLucide, 1000);
            }, 500);
        });

        // Monitor for errors
        window.addEventListener('error', function(e) {
            logToConsole('❌ JavaScript Error: ' + e.message, 'error');
        });

        // Monitor for CSP violations
        document.addEventListener('securitypolicyviolation', function(e) {
            logToConsole('❌ CSP Violation: ' + e.violatedDirective + ' - ' + e.blockedURI, 'error');
        });
    </script>
</body>
</html>
