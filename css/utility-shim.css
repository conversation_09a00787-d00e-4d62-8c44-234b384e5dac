/* Utility Shim to approximate Tailwind classes locally (subset for homepage) */
/* Layout */
.container { width: 100%; margin-left: auto; margin-right: auto; padding-left: 1rem; padding-right: 1rem; }
.max-w-5xl { max-width: 64rem; }
.max-w-6xl { max-width: 72rem; }
.max-w-7xl { max-width: 80rem; }
.w-full { width: 100%; }
.max-w-full { max-width: 100%; }
.mx-auto { margin-left: auto; margin-right: auto; }
.my-2 { margin-top: .5rem; margin-bottom: .5rem; }
.my-4 { margin-top: 1rem; margin-bottom: 1rem; }
.mt-2 { margin-top: .5rem; }
.mt-4 { margin-top: 1rem; }
.mt-6 { margin-top: 1.5rem; }
.mt-8 { margin-top: 2rem; }
.mb-2 { margin-bottom: .5rem; }
.mb-4 { margin-bottom: 1rem; }
.mb-6 { margin-bottom: 1.5rem; }
.mb-8 { margin-bottom: 2rem; }
.p-2 { padding: .5rem; }
.p-4 { padding: 1rem; }
.p-6 { padding: 1.5rem; }
.p-8 { padding: 2rem; }
.px-2 { padding-left: .5rem; padding-right: .5rem; }
.px-4 { padding-left: 1rem; padding-right: 1rem; }
.px-6 { padding-left: 1.5rem; padding-right: 1.5rem; }
.px-8 { padding-left: 2rem; padding-right: 2rem; }
.py-2 { padding-top: .5rem; padding-bottom: .5rem; }
.py-4 { padding-top: 1rem; padding-bottom: 1rem; }
.py-6 { padding-top: 1.5rem; padding-bottom: 1.5rem; }
.py-8 { padding-top: 2rem; padding-bottom: 2rem; }
.py-12 { padding-top: 3rem; padding-bottom: 3rem; }
.gap-2 { gap: .5rem; }
.gap-3 { gap: .75rem; }
.gap-4 { gap: 1rem; }
.gap-6 { gap: 1.5rem; }
.gap-8 { gap: 2rem; }
.gap-12 { gap: 3rem; }
.flex { display: flex; }
.inline-flex { display: inline-flex; }
.block { display: block; }
.inline-block { display: inline-block; }
.hidden { display: none; }
.items-center { align-items: center; }
.justify-center { justify-content: center; }
.justify-between { justify-content: space-between; }
.text-center { text-align: center; }

/* Grid */
.grid { display: grid; }
.grid-cols-1 { grid-template-columns: repeat(1, minmax(0, 1fr)); }
.grid-cols-2 { grid-template-columns: repeat(2, minmax(0, 1fr)); }
.grid-cols-3 { grid-template-columns: repeat(3, minmax(0, 1fr)); }
.grid-cols-4 { grid-template-columns: repeat(4, minmax(0, 1fr)); }

/* Responsive */
@media (min-width: 768px) {
  .md\:block { display: block; }
  .md\:hidden { display: none; }
  .md\:grid-cols-2 { grid-template-columns: repeat(2, minmax(0, 1fr)); }
  .md\:grid-cols-3 { grid-template-columns: repeat(3, minmax(0, 1fr)); }
  .md\:grid-cols-4 { grid-template-columns: repeat(4, minmax(0, 1fr)); }
  .md\:text-left { text-align: left; }
}

/* Typography */
.text-xs { font-size: .75rem; line-height: 1rem; }
.text-sm { font-size: .875rem; line-height: 1.25rem; }
.text-base { font-size: 1rem; line-height: 1.5rem; }
.text-lg { font-size: 1.125rem; line-height: 1.75rem; }
.text-xl { font-size: 1.25rem; line-height: 1.75rem; }
.text-2xl { font-size: 1.5rem; line-height: 2rem; }
.text-3xl { font-size: 1.875rem; line-height: 2.25rem; }
.text-4xl { font-size: 2.25rem; line-height: 2.5rem; }
.text-5xl { font-size: 3rem; line-height: 1; }
.font-medium { font-weight: 500; }
.font-semibold { font-weight: 600; }
.font-bold { font-weight: 700; }
.font-extrabold { font-weight: 800; }
.leading-tight { line-height: 1.25; }
.tracking-tight { letter-spacing: -0.015em; }

/* Colors */
:root {
  --slate-50:#f8fafc; --slate-100:#f1f5f9; --slate-200:#e2e8f0; --slate-300:#cbd5e1; --slate-400:#94a3b8; --slate-500:#64748b; --slate-600:#475569; --slate-700:#334155; --slate-800:#1f2937; --slate-900:#0f172a; --slate-950:#020617;
  --gray-300:#d1d5db; --white:#ffffff; --emerald-500:#10b981; --emerald-600:#059669; --blue-600:#2563eb; --blue-700:#1d4ed8; --red-500:#ef4444;
}
.text-white { color: var(--white); }
.text-slate-300 { color: var(--slate-300); }
.text-slate-400 { color: var(--slate-400); }
.text-gray-300 { color: var(--gray-300); }
.text-emerald-500 { color: var(--emerald-500); }
.text-blue-600 { color: var(--blue-600); }
.bg-slate-950 { background-color: var(--slate-950); }
.bg-slate-900 { background-color: var(--slate-900); }
.bg-slate-800 { background-color: var(--slate-800); }
.bg-emerald-500 { background-color: var(--emerald-500); }
.bg-blue-600 { background-color: var(--blue-600); }
.hover\:bg-emerald-600:hover { background-color: var(--emerald-600); }
.hover\:bg-blue-700:hover { background-color: var(--blue-700); }

/ * Borders & Radius */
.border { border-width: 1px; border-style: solid; border-color: rgba(148,163,184,0.3); }
.border-slate-700 { border-color: var(--slate-700); }
.rounded { border-radius: .25rem; }
.rounded-md { border-radius: .375rem; }
.rounded-lg { border-radius: .5rem; }
.rounded-xl { border-radius: .75rem; }
.rounded-2xl { border-radius: 1rem; }

/* Shadows */
.shadow { box-shadow: 0 1px 2px rgba(0,0,0,0.05); }
.shadow-md { box-shadow: 0 4px 6px rgba(0,0,0,0.1); }
.shadow-lg { box-shadow: 0 10px 15px rgba(0,0,0,0.2); }

/* Buttons */
.btn { display:inline-flex; align-items:center; justify-content:center; padding:.75rem 1.25rem; border-radius:.5rem; font-weight:600; }
.btn-primary { background-color: var(--emerald-500); color: var(--white); }
.btn-primary:hover { background-color: var(--emerald-600); }
.btn-secondary { background-color: var(--blue-600); color: var(--white); }
.btn-secondary:hover { background-color: var(--blue-700); }

/* Misc */
.cursor-pointer { cursor: pointer; }
.opacity-75 { opacity: .75; }
.opacity-0 { opacity: 0; }
.transition { transition: all .2s ease-in-out; }
.duration-200 { transition-duration: 200ms; }
.ease-in-out { transition-timing-function: cubic-bezier(0.4,0,0.2,1); }

/* Hero helpers used in this project */
.hero-title { font-size: 2.75rem; line-height: 1.1; font-weight: 800; letter-spacing: -0.02em; }
.hero-subtitle { color: var(--slate-300); font-size: 1.125rem; line-height: 1.75rem; }
.hero-cta { display:inline-flex; align-items:center; gap:.5rem; padding:.75rem 1.25rem; background: var(--emerald-500); color:#fff; border-radius:.5rem; font-weight:700; }
.hero-cta:hover { background: var(--emerald-600); }

