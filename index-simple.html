<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>X-ZoneServers - High-Performance Dedicated Servers & VPS Hosting</title>
    <meta name="description" content="Enterprise-grade dedicated servers and VPS hosting with 10Gbps networks, global locations, and 99.9% uptime SLA.">
    
    <!-- Inline CSS to avoid external file issues -->
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Arial, sans-serif;
            background: #020617;
            color: #d1d5db;
            line-height: 1.6;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 20px;
        }
        
        /* Header */
        .header {
            background: rgba(15, 23, 42, 0.95);
            backdrop-filter: blur(10px);
            padding: 1rem 0;
            position: sticky;
            top: 0;
            z-index: 100;
            border-bottom: 1px solid rgba(59, 130, 246, 0.2);
        }
        
        .nav {
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .logo {
            font-size: 1.5rem;
            font-weight: 700;
            color: #3b82f6;
            text-decoration: none;
        }
        
        .nav-links {
            display: flex;
            list-style: none;
            gap: 2rem;
        }
        
        .nav-links a {
            color: #d1d5db;
            text-decoration: none;
            font-weight: 500;
            transition: color 0.2s;
        }
        
        .nav-links a:hover {
            color: #3b82f6;
        }
        
        /* Hero Section */
        .hero {
            background: radial-gradient(ellipse at top, rgba(14, 165, 233, 0.15), transparent 60%);
            padding: 6rem 0;
            text-align: center;
        }
        
        .hero h1 {
            font-size: 3.5rem;
            font-weight: 900;
            margin-bottom: 1.5rem;
            background: linear-gradient(135deg, #3b82f6, #06b6d4);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }
        
        .hero p {
            font-size: 1.25rem;
            color: #9ca3af;
            margin-bottom: 2rem;
            max-width: 600px;
            margin-left: auto;
            margin-right: auto;
        }
        
        .cta-buttons {
            display: flex;
            gap: 1rem;
            justify-content: center;
            flex-wrap: wrap;
        }
        
        .btn {
            padding: 0.75rem 2rem;
            border-radius: 0.5rem;
            font-weight: 600;
            text-decoration: none;
            display: inline-block;
            transition: all 0.2s;
        }
        
        .btn-primary {
            background: linear-gradient(135deg, #3b82f6, #1d4ed8);
            color: white;
        }
        
        .btn-secondary {
            background: transparent;
            color: #3b82f6;
            border: 2px solid #3b82f6;
        }
        
        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 25px rgba(0, 0, 0, 0.3);
        }
        
        /* Features Section */
        .features {
            padding: 6rem 0;
            background: #0f172a;
        }
        
        .features h2 {
            text-align: center;
            font-size: 2.5rem;
            font-weight: 700;
            margin-bottom: 3rem;
            color: white;
        }
        
        .features-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 2rem;
        }
        
        .feature-card {
            background: rgba(30, 41, 59, 0.8);
            border: 1px solid rgba(59, 130, 246, 0.2);
            border-radius: 1rem;
            padding: 2rem;
            text-align: center;
            transition: transform 0.2s;
        }
        
        .feature-card:hover {
            transform: translateY(-5px);
            border-color: rgba(59, 130, 246, 0.5);
        }
        
        .feature-icon {
            font-size: 3rem;
            margin-bottom: 1rem;
            display: block;
        }
        
        .feature-card h3 {
            font-size: 1.5rem;
            font-weight: 600;
            margin-bottom: 1rem;
            color: white;
        }
        
        .feature-card p {
            color: #9ca3af;
        }
        
        /* Footer */
        .footer {
            background: #0f172a;
            border-top: 1px solid rgba(59, 130, 246, 0.2);
            padding: 3rem 0 1rem;
            text-align: center;
        }
        
        .footer-content {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 2rem;
            margin-bottom: 2rem;
        }
        
        .footer-section h4 {
            color: white;
            font-weight: 600;
            margin-bottom: 1rem;
        }
        
        .footer-section a {
            color: #9ca3af;
            text-decoration: none;
            display: block;
            margin-bottom: 0.5rem;
            transition: color 0.2s;
        }
        
        .footer-section a:hover {
            color: #3b82f6;
        }
        
        .footer-bottom {
            border-top: 1px solid rgba(59, 130, 246, 0.2);
            padding-top: 1rem;
            color: #6b7280;
        }
        
        /* Responsive */
        @media (max-width: 768px) {
            .hero h1 {
                font-size: 2.5rem;
            }
            
            .nav-links {
                display: none;
            }
            
            .cta-buttons {
                flex-direction: column;
                align-items: center;
            }
        }
    </style>
</head>
<body>
    <!-- Header -->
    <header class="header">
        <nav class="nav container">
            <a href="/" class="logo">X-ZoneServers</a>
            <ul class="nav-links">
                <li><a href="/dedicated">Dedicated Servers</a></li>
                <li><a href="/streaming-vps">Streaming VPS</a></li>
                <li><a href="/game-hosting">Game Hosting</a></li>
                <li><a href="/network">Network</a></li>
                <li><a href="/about">About</a></li>
            </ul>
        </nav>
    </header>

    <!-- Hero Section -->
    <section class="hero">
        <div class="container">
            <h1>Enterprise Hosting Solutions</h1>
            <p>High-performance dedicated servers and VPS hosting with 10Gbps networks, 99.9% uptime SLA, and global locations.</p>
            <div class="cta-buttons">
                <a href="/dedicated" class="btn btn-primary">View Dedicated Servers</a>
                <a href="/streaming-vps" class="btn btn-secondary">Explore VPS Plans</a>
            </div>
        </div>
    </section>

    <!-- Features Section -->
    <section class="features">
        <div class="container">
            <h2>Why Choose X-ZoneServers?</h2>
            <div class="features-grid">
                <div class="feature-card">
                    <span class="feature-icon">🚀</span>
                    <h3>10Gbps Network</h3>
                    <p>Ultra-fast network connectivity with premium bandwidth and low latency worldwide.</p>
                </div>
                <div class="feature-card">
                    <span class="feature-icon">🛡️</span>
                    <h3>99.9% Uptime SLA</h3>
                    <p>Enterprise-grade reliability with guaranteed uptime and 24/7 monitoring.</p>
                </div>
                <div class="feature-card">
                    <span class="feature-icon">🌍</span>
                    <h3>Global Locations</h3>
                    <p>13 datacenter locations worldwide for optimal performance and redundancy.</p>
                </div>
                <div class="feature-card">
                    <span class="feature-icon">⚡</span>
                    <h3>Instant Deployment</h3>
                    <p>Get your server up and running in minutes with automated provisioning.</p>
                </div>
                <div class="feature-card">
                    <span class="feature-icon">🔧</span>
                    <h3>Full Root Access</h3>
                    <p>Complete control over your server with root access and custom configurations.</p>
                </div>
                <div class="feature-card">
                    <span class="feature-icon">💬</span>
                    <h3>24/7 Support</h3>
                    <p>Expert technical support available around the clock via multiple channels.</p>
                </div>
            </div>
        </div>
    </section>

    <!-- Footer -->
    <footer class="footer">
        <div class="container">
            <div class="footer-content">
                <div class="footer-section">
                    <h4>Services</h4>
                    <a href="/dedicated">Dedicated Servers</a>
                    <a href="/streaming-vps">Streaming VPS</a>
                    <a href="/game-hosting">Game Hosting</a>
                    <a href="/shared-vps">Shared VPS</a>
                </div>
                <div class="footer-section">
                    <h4>Company</h4>
                    <a href="/about">About Us</a>
                    <a href="/network">Network</a>
                    <a href="/locations">Locations</a>
                    <a href="/use-cases">Use Cases</a>
                </div>
                <div class="footer-section">
                    <h4>Support</h4>
                    <a href="/contact">Contact</a>
                    <a href="/kb">Knowledge Base</a>
                    <a href="/blog">Blog</a>
                </div>
                <div class="footer-section">
                    <h4>Legal</h4>
                    <a href="/terms-and-conditions">Terms & Conditions</a>
                    <a href="/privacy-policy">Privacy Policy</a>
                    <a href="/cookie-policy">Cookie Policy</a>
                </div>
            </div>
            <div class="footer-bottom">
                <p>&copy; 2025 X-ZoneServers. All rights reserved.</p>
            </div>
        </div>
    </footer>

    <!-- Minimal JavaScript -->
    <script>
        // Simple analytics-free tracking
        console.log('✅ X-ZoneServers homepage loaded successfully');
        console.log('🌐 Domain:', window.location.hostname);
        console.log('⏰ Load time:', new Date().toLocaleTimeString());
        
        // Simple navigation tracking
        document.querySelectorAll('a[href^="/"]').forEach(link => {
            link.addEventListener('click', function(e) {
                console.log('🔗 Navigation:', this.getAttribute('href'));
            });
        });
        
        // Performance monitoring
        window.addEventListener('load', function() {
            const loadTime = performance.now();
            console.log(`⚡ Page loaded in ${loadTime.toFixed(2)}ms`);
        });
    </script>
</body>
</html>
