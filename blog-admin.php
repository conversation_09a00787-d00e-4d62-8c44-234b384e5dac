<?php
/**
 * Blog Admin Interface for WHMCS
 * SEO-optimized blog post management interface
 */

require_once 'whmcs-blog-module.php';

// Initialize blog module
$blogModule = init_whmcs_blog_module();

// Handle form submissions
$message = '';
$messageType = '';

if ($_POST) {
    switch ($_POST['action']) {
        case 'install':
            $result = $blogModule->installModule();
            $message = $result['message'];
            $messageType = $result['success'] ? 'success' : 'error';
            break;
            
        case 'create_post':
            $postData = [
                'title' => $_POST['title'],
                'slug' => $_POST['slug'] ?? '',
                'content' => $_POST['content'],
                'excerpt' => $_POST['excerpt'] ?? '',
                'featured_image' => $_POST['featured_image'] ?? '',
                'meta_title' => $_POST['meta_title'] ?? '',
                'meta_description' => $_POST['meta_description'] ?? '',
                'keywords' => $_POST['keywords'] ?? '',
                'author_name' => $_POST['author_name'],
                'author_email' => $_POST['author_email'],
                'author_title' => $_POST['author_title'] ?? '',
                'author_image' => $_POST['author_image'] ?? '',
                'category' => $_POST['category'],
                'tags' => isset($_POST['tags']) ? explode(',', $_POST['tags']) : [],
                'status' => $_POST['status'] ?? 'draft',
                'published_at' => $_POST['status'] === 'published' ? date('Y-m-d H:i:s') : null
            ];
            
            $result = $blogModule->createBlogPost($postData);
            $message = $result['message'];
            $messageType = $result['success'] ? 'success' : 'error';
            break;
    }
}

// Get existing posts
$postsResult = $blogModule->getBlogPosts(['limit' => 50]);
$posts = $postsResult['success'] ? $postsResult['posts'] : [];
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Blog Admin - X-ZoneServers</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script src="https://unpkg.com/lucide@latest" defer></script>
    <style>
        .message-success { @apply bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded mb-4; }
        .message-error { @apply bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-4; }
    </style>
</head>
<body class="bg-gray-100 min-h-screen">
    <div class="container mx-auto px-4 py-8">
        <div class="bg-white rounded-lg shadow-lg">
            <!-- Header -->
            <div class="bg-blue-600 text-white p-6 rounded-t-lg">
                <h1 class="text-2xl font-bold flex items-center">
                    <svg class="w-8 h-8 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.746 0 3.332.477 4.5 1.253v13C19.832 18.477 18.246 18 16.5 18c-1.746 0-3.332.477-4.5 1.253"></path>
                    </svg>
                    X-ZoneServers Blog Admin
                </h1>
                <p class="text-blue-100 mt-2">SEO-optimized blog management interface</p>
            </div>

            <!-- Messages -->
            <?php if ($message): ?>
                <div class="p-6 pb-0">
                    <div class="message-<?php echo $messageType; ?>">
                        <?php echo htmlspecialchars($message); ?>
                    </div>
                </div>
            <?php endif; ?>

            <!-- Navigation Tabs -->
            <div class="p-6">
                <div class="border-b border-gray-200 mb-6">
                    <nav class="-mb-px flex space-x-8">
                        <button onclick="showTab('posts')" id="tab-posts" class="tab-button border-blue-500 text-blue-600 border-b-2 py-2 px-1 text-sm font-medium">
                            Blog Posts
                        </button>
                        <button onclick="showTab('create')" id="tab-create" class="tab-button border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300 border-b-2 py-2 px-1 text-sm font-medium">
                            Create Post
                        </button>
                        <button onclick="showTab('setup')" id="tab-setup" class="tab-button border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300 border-b-2 py-2 px-1 text-sm font-medium">
                            Setup
                        </button>
                    </nav>
                </div>

                <!-- Blog Posts Tab -->
                <div id="content-posts" class="tab-content">
                    <div class="mb-6">
                        <h2 class="text-xl font-semibold text-gray-900 mb-4">Existing Blog Posts</h2>
                        
                        <?php if (empty($posts)): ?>
                            <div class="text-center py-12">
                                <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                                </svg>
                                <h3 class="mt-2 text-sm font-medium text-gray-900">No blog posts</h3>
                                <p class="mt-1 text-sm text-gray-500">Get started by creating your first blog post.</p>
                                <button onclick="showTab('create')" class="mt-4 bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700">
                                    Create Post
                                </button>
                            </div>
                        <?php else: ?>
                            <div class="grid gap-6">
                                <?php foreach ($posts as $post): ?>
                                    <div class="bg-gray-50 rounded-lg p-6">
                                        <div class="flex justify-between items-start">
                                            <div class="flex-1">
                                                <h3 class="text-lg font-semibold text-gray-900 mb-2">
                                                    <?php echo htmlspecialchars($post->title); ?>
                                                </h3>
                                                <p class="text-gray-600 mb-3">
                                                    <?php echo htmlspecialchars($post->excerpt); ?>
                                                </p>
                                                <div class="flex flex-wrap items-center gap-4 text-sm text-gray-500">
                                                    <span class="flex items-center">
                                                        <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"></path>
                                                        </svg>
                                                        <?php echo htmlspecialchars($post->author_name); ?>
                                                    </span>
                                                    <span class="flex items-center">
                                                        <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 7h.01M7 3h5c.512 0 1.024.195 1.414.586l7 7a2 2 0 010 2.828l-7 7a2 2 0 01-2.828 0l-7-7A1.994 1.994 0 013 12V7a4 4 0 014-4z"></path>
                                                        </svg>
                                                        <?php echo htmlspecialchars($post->category); ?>
                                                    </span>
                                                    <span class="flex items-center">
                                                        <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                                        </svg>
                                                        <?php echo $post->read_time; ?> min read
                                                    </span>
                                                    <span class="flex items-center">
                                                        <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                                                        </svg>
                                                        <?php echo number_format($post->word_count); ?> words
                                                    </span>
                                                </div>
                                            </div>
                                            <div class="ml-4 flex flex-col items-end">
                                                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium 
                                                    <?php echo $post->status === 'published' ? 'bg-green-100 text-green-800' : ($post->status === 'draft' ? 'bg-yellow-100 text-yellow-800' : 'bg-gray-100 text-gray-800'); ?>">
                                                    <?php echo ucfirst($post->status); ?>
                                                </span>
                                                <div class="mt-2">
                                                    <a href="/blog/<?php echo htmlspecialchars($post->slug); ?>" target="_blank" 
                                                       class="text-blue-600 hover:text-blue-800 text-sm">View Post</a>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                <?php endforeach; ?>
                            </div>
                        <?php endif; ?>
                    </div>
                </div>

                <!-- Create Post Tab -->
                <div id="content-create" class="tab-content hidden">
                    <h2 class="text-xl font-semibold text-gray-900 mb-6">Create New Blog Post</h2>
                    
                    <form method="POST" class="space-y-6">
                        <input type="hidden" name="action" value="create_post">
                        
                        <!-- Basic Information -->
                        <div class="bg-gray-50 p-6 rounded-lg">
                            <h3 class="text-lg font-medium text-gray-900 mb-4">Basic Information</h3>
                            
                            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                                <div>
                                    <label for="title" class="block text-sm font-medium text-gray-700 mb-2">Title *</label>
                                    <input type="text" name="title" id="title" required
                                           class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                                           placeholder="Enter blog post title">
                                </div>
                                
                                <div>
                                    <label for="slug" class="block text-sm font-medium text-gray-700 mb-2">URL Slug</label>
                                    <input type="text" name="slug" id="slug"
                                           class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                                           placeholder="auto-generated-from-title">
                                    <p class="text-xs text-gray-500 mt-1">Leave blank to auto-generate from title</p>
                                </div>
                            </div>
                            
                            <div class="mt-6">
                                <label for="excerpt" class="block text-sm font-medium text-gray-700 mb-2">Excerpt</label>
                                <textarea name="excerpt" id="excerpt" rows="3"
                                          class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                                          placeholder="Brief description of the post (auto-generated if left empty)"></textarea>
                            </div>
                        </div>

                        <!-- Content -->
                        <div class="bg-gray-50 p-6 rounded-lg">
                            <h3 class="text-lg font-medium text-gray-900 mb-4">Content</h3>
                            
                            <div>
                                <label for="content" class="block text-sm font-medium text-gray-700 mb-2">Blog Content *</label>
                                <textarea name="content" id="content" rows="15" required
                                          class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                                          placeholder="Enter your blog post content here..."></textarea>
                                <p class="text-xs text-gray-500 mt-1">You can use HTML tags for formatting</p>
                            </div>
                            
                            <div class="mt-6">
                                <label for="featured_image" class="block text-sm font-medium text-gray-700 mb-2">Featured Image URL</label>
                                <input type="url" name="featured_image" id="featured_image"
                                       class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                                       placeholder="https://example.com/image.jpg">
                            </div>
                        </div>

                        <!-- SEO Settings -->
                        <div class="bg-gray-50 p-6 rounded-lg">
                            <h3 class="text-lg font-medium text-gray-900 mb-4">SEO Settings</h3>
                            
                            <div class="space-y-4">
                                <div>
                                    <label for="meta_title" class="block text-sm font-medium text-gray-700 mb-2">Meta Title</label>
                                    <input type="text" name="meta_title" id="meta_title" maxlength="60"
                                           class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                                           placeholder="SEO title (defaults to post title)">
                                    <p class="text-xs text-gray-500 mt-1">Recommended: 50-60 characters</p>
                                </div>
                                
                                <div>
                                    <label for="meta_description" class="block text-sm font-medium text-gray-700 mb-2">Meta Description</label>
                                    <textarea name="meta_description" id="meta_description" rows="2" maxlength="160"
                                              class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                                              placeholder="Brief description for search engines (defaults to excerpt)"></textarea>
                                    <p class="text-xs text-gray-500 mt-1">Recommended: 150-160 characters</p>
                                </div>
                                
                                <div>
                                    <label for="keywords" class="block text-sm font-medium text-gray-700 mb-2">Keywords</label>
                                    <input type="text" name="keywords" id="keywords"
                                           class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                                           placeholder="keyword1, keyword2, keyword3">
                                    <p class="text-xs text-gray-500 mt-1">Comma-separated list of keywords</p>
                                </div>
                            </div>
                        </div>

                        <!-- Author & Categorization -->
                        <div class="bg-gray-50 p-6 rounded-lg">
                            <h3 class="text-lg font-medium text-gray-900 mb-4">Author & Categorization</h3>
                            
                            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                                <div>
                                    <label for="author_name" class="block text-sm font-medium text-gray-700 mb-2">Author Name *</label>
                                    <input type="text" name="author_name" id="author_name" required
                                           class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                                           placeholder="John Smith">
                                </div>
                                
                                <div>
                                    <label for="author_email" class="block text-sm font-medium text-gray-700 mb-2">Author Email *</label>
                                    <input type="email" name="author_email" id="author_email" required
                                           class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                                           placeholder="<EMAIL>">
                                </div>
                                
                                <div>
                                    <label for="author_title" class="block text-sm font-medium text-gray-700 mb-2">Author Title</label>
                                    <input type="text" name="author_title" id="author_title"
                                           class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                                           placeholder="Chief Technology Officer">
                                </div>
                                
                                <div>
                                    <label for="category" class="block text-sm font-medium text-gray-700 mb-2">Category *</label>
                                    <select name="category" id="category" required
                                            class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                                        <option value="">Select Category</option>
                                        <option value="server-administration">Server Administration</option>
                                        <option value="network-optimization">Network Optimization</option>
                                        <option value="security-compliance">Security & Compliance</option>
                                        <option value="case-studies">Case Studies</option>
                                    </select>
                                </div>
                            </div>
                            
                            <div class="mt-6">
                                <label for="tags" class="block text-sm font-medium text-gray-700 mb-2">Tags</label>
                                <input type="text" name="tags" id="tags"
                                       class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                                       placeholder="dedicated servers, VPS hosting, performance">
                                <p class="text-xs text-gray-500 mt-1">Comma-separated list of tags</p>
                            </div>
                        </div>

                        <!-- Publishing Options -->
                        <div class="bg-gray-50 p-6 rounded-lg">
                            <h3 class="text-lg font-medium text-gray-900 mb-4">Publishing Options</h3>
                            
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2">Status</label>
                                <div class="space-y-2">
                                    <label class="flex items-center">
                                        <input type="radio" name="status" value="draft" checked
                                               class="w-4 h-4 text-blue-600 border-gray-300 focus:ring-blue-500">
                                        <span class="ml-2 text-sm text-gray-700">Save as Draft</span>
                                    </label>
                                    <label class="flex items-center">
                                        <input type="radio" name="status" value="published"
                                               class="w-4 h-4 text-blue-600 border-gray-300 focus:ring-blue-500">
                                        <span class="ml-2 text-sm text-gray-700">Publish Now</span>
                                    </label>
                                </div>
                            </div>
                        </div>

                        <div class="flex justify-end space-x-4">
                            <button type="button" onclick="showTab('posts')"
                                    class="px-6 py-2 border border-gray-300 text-gray-700 rounded-md hover:bg-gray-50">
                                Cancel
                            </button>
                            <button type="submit"
                                    class="px-6 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700">
                                Create Blog Post
                            </button>
                        </div>
                    </form>
                </div>

                <!-- Setup Tab -->
                <div id="content-setup" class="tab-content hidden">
                    <h2 class="text-xl font-semibold text-gray-900 mb-6">Module Setup</h2>
                    
                    <div class="bg-yellow-50 border border-yellow-200 rounded-lg p-6 mb-6">
                        <div class="flex items-start">
                            <svg class="w-5 h-5 text-yellow-400 mt-0.5 mr-3" fill="currentColor" viewBox="0 0 20 20">
                                <path fill-rule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clip-rule="evenodd"></path>
                            </svg>
                            <div>
                                <h3 class="text-sm font-medium text-yellow-800">Setup Required</h3>
                                <p class="mt-1 text-sm text-yellow-700">
                                    Before using the blog system, you need to install the database tables and configure WHMCS integration.
                                </p>
                            </div>
                        </div>
                    </div>
                    
                    <div class="space-y-6">
                        <div class="bg-white border border-gray-200 rounded-lg p-6">
                            <h3 class="text-lg font-medium text-gray-900 mb-4">Install Blog Module</h3>
                            <p class="text-gray-600 mb-4">
                                This will create the necessary database tables in your WHMCS database to store blog posts, categories, and tags.
                            </p>
                            
                            <form method="POST" class="inline">
                                <input type="hidden" name="action" value="install">
                                <button type="submit" class="bg-green-600 text-white px-4 py-2 rounded-md hover:bg-green-700">
                                    Install Database Tables
                                </button>
                            </form>
                        </div>
                        
                        <div class="bg-white border border-gray-200 rounded-lg p-6">
                            <h3 class="text-lg font-medium text-gray-900 mb-4">Configuration</h3>
                            <div class="space-y-4 text-sm text-gray-600">
                                <div class="flex items-start">
                                    <span class="w-2 h-2 bg-blue-500 rounded-full mt-2 mr-3"></span>
                                    <div>
                                        <strong>WHMCS Integration:</strong> Update your <code class="bg-gray-100 px-2 py-1 rounded">whmcs-config.php</code> 
                                        file with your WHMCS API credentials.
                                    </div>
                                </div>
                                <div class="flex items-start">
                                    <span class="w-2 h-2 bg-blue-500 rounded-full mt-2 mr-3"></span>
                                    <div>
                                        <strong>URL Rewriting:</strong> Configure your web server to route blog URLs to the frontend display script.
                                    </div>
                                </div>
                                <div class="flex items-start">
                                    <span class="w-2 h-2 bg-blue-500 rounded-full mt-2 mr-3"></span>
                                    <div>
                                        <strong>SEO Setup:</strong> Ensure proper meta tags and structured data are implemented in your frontend templates.
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        function showTab(tabName) {
            // Hide all tab contents
            document.querySelectorAll('.tab-content').forEach(content => {
                content.classList.add('hidden');
            });
            
            // Remove active classes from all tab buttons
            document.querySelectorAll('.tab-button').forEach(button => {
                button.classList.remove('border-blue-500', 'text-blue-600');
                button.classList.add('border-transparent', 'text-gray-500');
            });
            
            // Show selected tab content
            document.getElementById('content-' + tabName).classList.remove('hidden');
            
            // Add active classes to selected tab button
            const activeButton = document.getElementById('tab-' + tabName);
            activeButton.classList.add('border-blue-500', 'text-blue-600');
            activeButton.classList.remove('border-transparent', 'text-gray-500');
        }
        
        // Auto-generate slug from title
        document.getElementById('title')?.addEventListener('input', function() {
            const title = this.value;
            const slug = title.toLowerCase()
                .replace(/[^a-z0-9\s-]/g, '')
                .replace(/\s+/g, '-')
                .replace(/-+/g, '-')
                .trim('-');
            document.getElementById('slug').value = slug;
        });
    </script>
</body>
</html>