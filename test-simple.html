<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Simple Test - No External Resources</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            background: #1a1a1a;
            color: white;
            padding: 20px;
            margin: 0;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
        }
        .success {
            background: #0f5132;
            border: 1px solid #198754;
            padding: 15px;
            border-radius: 5px;
            margin: 20px 0;
        }
        .info {
            background: #055160;
            border: 1px solid #0dcaf0;
            padding: 15px;
            border-radius: 5px;
            margin: 20px 0;
        }
        .error {
            background: #58151c;
            border: 1px solid #dc3545;
            padding: 15px;
            border-radius: 5px;
            margin: 20px 0;
        }
        button {
            background: #0d6efd;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background: #0b5ed7;
        }
        .log {
            background: #000;
            color: #0f0;
            padding: 10px;
            border-radius: 5px;
            font-family: monospace;
            font-size: 12px;
            max-height: 200px;
            overflow-y: auto;
            margin: 10px 0;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🧪 Simple Redirect Test</h1>
        
        <div class="success">
            <h2>✅ SUCCESS: This page loaded without redirects!</h2>
            <p>If you can see this page, the basic redirect issue is fixed.</p>
        </div>

        <div class="info">
            <h3>📊 Test Results</h3>
            <div id="test-results">
                <p><strong>Current URL:</strong> <span id="current-url"></span></p>
                <p><strong>Page loaded at:</strong> <span id="load-time"></span></p>
                <p><strong>No external resources:</strong> This page uses only inline CSS and JavaScript</p>
            </div>
        </div>

        <div class="info">
            <h3>🔗 Test Other Files</h3>
            <p>Try these links to test if other HTML files work:</p>
            <button onclick="testLink('/test-cloudflare-fix.html')">Test Cloudflare Fix</button>
            <button onclick="testLink('/test-redirect-fix.html')">Test Redirect Fix</button>
            <button onclick="testLink('/test-comprehensive.html')">Test Comprehensive</button>
            <button onclick="testLink('/404.html')">Test 404 Page</button>
        </div>

        <div class="info">
            <h3>📝 Console Log</h3>
            <div id="console-log" class="log">
                <div>Test initialized...</div>
            </div>
        </div>

        <div class="info">
            <h3>🏠 Navigation</h3>
            <button onclick="window.location.href='/'">Go to Homepage</button>
            <button onclick="window.location.href='/dedicated'">Dedicated Servers</button>
        </div>
    </div>

    <script>
        // Simple logging function
        function log(message, type = 'info') {
            const logDiv = document.getElementById('console-log');
            const timestamp = new Date().toLocaleTimeString();
            const color = type === 'error' ? '#ff6b6b' : 
                         type === 'success' ? '#51cf66' : 
                         type === 'warning' ? '#ffd43b' : '#74c0fc';
            
            logDiv.innerHTML += `<div style="color: ${color}">[${timestamp}] ${message}</div>`;
            logDiv.scrollTop = logDiv.scrollHeight;
        }

        // Test link function
        function testLink(url) {
            log(`Testing link: ${url}`, 'info');
            
            // Try to open in new tab first
            const newWindow = window.open(url, '_blank');
            
            if (newWindow) {
                log(`Opened ${url} in new tab`, 'success');
            } else {
                log(`Popup blocked, navigating to ${url}`, 'warning');
                window.location.href = url;
            }
        }

        // Initialize page
        document.addEventListener('DOMContentLoaded', function() {
            // Display current URL and load time
            document.getElementById('current-url').textContent = window.location.href;
            document.getElementById('load-time').textContent = new Date().toLocaleString();
            
            log('✅ Simple test page loaded successfully', 'success');
            log(`URL: ${window.location.href}`, 'info');
            log('No redirects detected', 'success');
            
            // Check if URL contains any redirect artifacts
            if (window.location.href.includes('/.html/') || 
                window.location.href.includes('/test-simple/')) {
                log('❌ WARNING: URL shows signs of redirect issues', 'error');
            } else {
                log('✅ URL looks clean - no redirect artifacts', 'success');
            }
        });

        // Monitor for errors
        window.addEventListener('error', function(e) {
            log(`❌ JavaScript Error: ${e.message}`, 'error');
        });

        // Log successful load
        log('JavaScript execution successful', 'success');
    </script>
</body>
</html>
