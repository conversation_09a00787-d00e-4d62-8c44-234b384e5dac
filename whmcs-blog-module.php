<?php
/**
 * WHMCS Blog Module
 * Custom module for storing and managing blog posts in WHMCS
 * 
 * This module creates custom database tables and provides functions
 * for managing SEO-optimized blog posts stored in WHMCS
 */

if (!defined("WHMCS"))
    die("This file cannot be accessed directly");

use WHMCS\Database\Capsule;

class WhmcsBlogModule {
    
    private $config;
    
    public function __construct($whmcsConfig = null) {
        if ($whmcsConfig) {
            $this->config = $whmcsConfig;
        } else {
            $this->config = require_once 'whmcs-config.php';
        }
    }
    
    /**
     * Create blog tables in WHMCS database
     */
    public function installModule() {
        try {
            // Create blog_posts table
            Capsule::schema()->create('mod_blog_posts', function ($table) {
                $table->increments('id');
                $table->string('title');
                $table->string('slug')->unique();
                $table->longText('content');
                $table->text('excerpt')->nullable();
                $table->string('featured_image')->nullable();
                $table->string('meta_title')->nullable();
                $table->text('meta_description')->nullable();
                $table->text('keywords')->nullable();
                $table->string('author_name');
                $table->string('author_email');
                $table->string('author_title')->nullable();
                $table->string('author_image')->nullable();
                $table->string('category');
                $table->json('tags')->nullable();
                $table->enum('status', ['draft', 'published', 'archived'])->default('draft');
                $table->integer('read_time')->nullable(); // in minutes
                $table->integer('word_count')->nullable();
                $table->timestamp('published_at')->nullable();
                $table->timestamps();
                
                // Indexes for SEO and performance
                $table->index(['slug']);
                $table->index(['status', 'published_at']);
                $table->index(['category']);
            });
            
            // Create blog_categories table
            Capsule::schema()->create('mod_blog_categories', function ($table) {
                $table->increments('id');
                $table->string('name');
                $table->string('slug')->unique();
                $table->text('description')->nullable();
                $table->string('color', 7)->default('#3B82F6'); // Hex color
                $table->integer('post_count')->default(0);
                $table->timestamps();
            });
            
            // Create blog_tags table
            Capsule::schema()->create('mod_blog_tags', function ($table) {
                $table->increments('id');
                $table->string('name');
                $table->string('slug')->unique();
                $table->integer('post_count')->default(0);
                $table->timestamps();
            });
            
            // Insert default categories
            $this->insertDefaultCategories();
            
            return ['success' => true, 'message' => 'Blog module installed successfully'];
            
        } catch (Exception $e) {
            return ['success' => false, 'message' => 'Installation failed: ' . $e->getMessage()];
        }
    }
    
    /**
     * Remove blog tables from WHMCS database
     */
    public function uninstallModule() {
        try {
            Capsule::schema()->dropIfExists('mod_blog_posts');
            Capsule::schema()->dropIfExists('mod_blog_categories');
            Capsule::schema()->dropIfExists('mod_blog_tags');
            
            return ['success' => true, 'message' => 'Blog module uninstalled successfully'];
            
        } catch (Exception $e) {
            return ['success' => false, 'message' => 'Uninstallation failed: ' . $e->getMessage()];
        }
    }
    
    /**
     * Create a new blog post
     */
    public function createBlogPost($data) {
        try {
            // Generate slug if not provided
            if (empty($data['slug'])) {
                $data['slug'] = $this->generateSlug($data['title']);
            }
            
            // Calculate read time and word count
            $wordCount = str_word_count(strip_tags($data['content']));
            $readTime = max(1, ceil($wordCount / 200)); // Average reading speed 200 words/min
            
            // Auto-generate excerpt if not provided
            if (empty($data['excerpt'])) {
                $data['excerpt'] = $this->generateExcerpt($data['content']);
            }
            
            // Prepare published_at timestamp
            $publishedAt = null;
            if ($data['status'] === 'published') {
                $publishedAt = $data['published_at'] ?? now();
            }
            
            $postId = Capsule::table('mod_blog_posts')->insertGetId([
                'title' => $data['title'],
                'slug' => $data['slug'],
                'content' => $data['content'],
                'excerpt' => $data['excerpt'],
                'featured_image' => $data['featured_image'] ?? null,
                'meta_title' => $data['meta_title'] ?? $data['title'],
                'meta_description' => $data['meta_description'] ?? $data['excerpt'],
                'keywords' => $data['keywords'] ?? null,
                'author_name' => $data['author_name'],
                'author_email' => $data['author_email'],
                'author_title' => $data['author_title'] ?? null,
                'author_image' => $data['author_image'] ?? null,
                'category' => $data['category'],
                'tags' => json_encode($data['tags'] ?? []),
                'status' => $data['status'] ?? 'draft',
                'read_time' => $readTime,
                'word_count' => $wordCount,
                'published_at' => $publishedAt,
                'created_at' => now(),
                'updated_at' => now()
            ]);
            
            // Update category post count
            $this->updateCategoryCount($data['category']);
            
            // Update tag counts
            if (!empty($data['tags'])) {
                $this->updateTagCounts($data['tags']);
            }
            
            return ['success' => true, 'post_id' => $postId, 'message' => 'Blog post created successfully'];
            
        } catch (Exception $e) {
            return ['success' => false, 'message' => 'Failed to create blog post: ' . $e->getMessage()];
        }
    }
    
    /**
     * Get blog posts with SEO data
     */
    public function getBlogPosts($filters = []) {
        try {
            $query = Capsule::table('mod_blog_posts');
            
            // Apply filters
            if (!empty($filters['status'])) {
                $query->where('status', $filters['status']);
            }
            
            if (!empty($filters['category'])) {
                $query->where('category', $filters['category']);
            }
            
            if (!empty($filters['limit'])) {
                $query->limit($filters['limit']);
            }
            
            // Default ordering for SEO (recent posts first)
            $query->orderBy('published_at', 'desc')
                  ->orderBy('created_at', 'desc');
            
            $posts = $query->get();
            
            // Decode JSON tags for each post
            foreach ($posts as $post) {
                $post->tags = json_decode($post->tags, true) ?? [];
            }
            
            return ['success' => true, 'posts' => $posts];
            
        } catch (Exception $e) {
            return ['success' => false, 'message' => 'Failed to fetch blog posts: ' . $e->getMessage()];
        }
    }
    
    /**
     * Get single blog post by slug (SEO-friendly)
     */
    public function getBlogPostBySlug($slug) {
        try {
            $post = Capsule::table('mod_blog_posts')
                          ->where('slug', $slug)
                          ->where('status', 'published')
                          ->first();
            
            if (!$post) {
                return ['success' => false, 'message' => 'Blog post not found'];
            }
            
            $post->tags = json_decode($post->tags, true) ?? [];
            
            return ['success' => true, 'post' => $post];
            
        } catch (Exception $e) {
            return ['success' => false, 'message' => 'Failed to fetch blog post: ' . $e->getMessage()];
        }
    }
    
    /**
     * Generate SEO sitemap data
     */
    public function getSitemapData() {
        try {
            $posts = Capsule::table('mod_blog_posts')
                           ->select(['slug', 'updated_at', 'published_at'])
                           ->where('status', 'published')
                           ->orderBy('published_at', 'desc')
                           ->get();
            
            return ['success' => true, 'posts' => $posts];
            
        } catch (Exception $e) {
            return ['success' => false, 'message' => 'Failed to generate sitemap data: ' . $e->getMessage()];
        }
    }
    
    /**
     * Helper functions
     */
    private function generateSlug($title) {
        $slug = strtolower(trim($title));
        $slug = preg_replace('/[^a-z0-9-]/', '-', $slug);
        $slug = preg_replace('/-+/', '-', $slug);
        $slug = trim($slug, '-');
        
        // Check if slug exists and make it unique
        $originalSlug = $slug;
        $counter = 1;
        
        while (Capsule::table('mod_blog_posts')->where('slug', $slug)->exists()) {
            $slug = $originalSlug . '-' . $counter;
            $counter++;
        }
        
        return $slug;
    }
    
    private function generateExcerpt($content, $length = 160) {
        $text = strip_tags($content);
        if (strlen($text) > $length) {
            $text = substr($text, 0, $length);
            $text = substr($text, 0, strrpos($text, ' ')) . '...';
        }
        return $text;
    }
    
    private function insertDefaultCategories() {
        $categories = [
            ['name' => 'Server Administration', 'slug' => 'server-administration', 'description' => 'Setup, configuration, and management guides for dedicated servers and VPS', 'color' => '#3B82F6'],
            ['name' => 'Network Optimization', 'slug' => 'network-optimization', 'description' => 'Performance tuning, latency reduction, and bandwidth optimization techniques', 'color' => '#10B981'],
            ['name' => 'Security & Compliance', 'slug' => 'security-compliance', 'description' => 'Enterprise security practices, compliance guides, and threat protection', 'color' => '#EF4444'],
            ['name' => 'Case Studies', 'slug' => 'case-studies', 'description' => 'Real-world implementations and success stories from enterprise clients', 'color' => '#8B5CF6']
        ];
        
        foreach ($categories as $category) {
            Capsule::table('mod_blog_categories')->insert(array_merge($category, [
                'created_at' => now(),
                'updated_at' => now()
            ]));
        }
    }
    
    private function updateCategoryCount($categorySlug) {
        $count = Capsule::table('mod_blog_posts')
                       ->where('category', $categorySlug)
                       ->where('status', 'published')
                       ->count();
        
        Capsule::table('mod_blog_categories')
               ->where('slug', $categorySlug)
               ->update(['post_count' => $count]);
    }
    
    private function updateTagCounts($tags) {
        foreach ($tags as $tag) {
            $tagSlug = $this->generateSlug($tag);
            
            // Insert or update tag
            $existingTag = Capsule::table('mod_blog_tags')->where('slug', $tagSlug)->first();
            
            if ($existingTag) {
                $count = Capsule::table('mod_blog_posts')
                               ->whereJsonContains('tags', $tag)
                               ->where('status', 'published')
                               ->count();
                
                Capsule::table('mod_blog_tags')
                       ->where('slug', $tagSlug)
                       ->update(['post_count' => $count]);
            } else {
                Capsule::table('mod_blog_tags')->insert([
                    'name' => $tag,
                    'slug' => $tagSlug,
                    'post_count' => 1,
                    'created_at' => now(),
                    'updated_at' => now()
                ]);
            }
        }
    }
}

// Initialize the module
function init_whmcs_blog_module() {
    return new WhmcsBlogModule();
}
?>