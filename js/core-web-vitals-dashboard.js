/**
 * Core Web Vitals Dashboard
 * Real-time performance monitoring dashboard for development and debugging
 */

class CoreWebVitalsDashboard {
    constructor() {
        this.metrics = new Map();
        this.isVisible = false;
        this.updateInterval = null;
        this.init();
    }

    init() {
        // Only show in development or when explicitly enabled
        if (this.shouldShowDashboard()) {
            this.createDashboard();
            this.startMonitoring();
            this.setupKeyboardShortcut();
        }
    }

    shouldShowDashboard() {
        return (
            window.location.hostname === 'localhost' ||
            window.location.hostname.includes('dev') ||
            window.location.search.includes('debug=performance') ||
            localStorage.getItem('show-performance-dashboard') === 'true'
        );
    }

    createDashboard() {
        const dashboard = document.createElement('div');
        dashboard.id = 'core-web-vitals-dashboard';
        dashboard.innerHTML = `
            <div class="cwv-dashboard">
                <div class="cwv-header">
                    <h3>Core Web Vitals</h3>
                    <div class="cwv-controls">
                        <button class="cwv-toggle" onclick="window.cwvDashboard.toggle()">−</button>
                        <button class="cwv-close" onclick="window.cwvDashboard.hide()">×</button>
                    </div>
                </div>
                <div class="cwv-content">
                    <div class="cwv-metrics">
                        <div class="cwv-metric" id="cwv-lcp">
                            <div class="cwv-metric-label">LCP</div>
                            <div class="cwv-metric-value">-</div>
                            <div class="cwv-metric-status"></div>
                        </div>
                        <div class="cwv-metric" id="cwv-fid">
                            <div class="cwv-metric-label">FID</div>
                            <div class="cwv-metric-value">-</div>
                            <div class="cwv-metric-status"></div>
                        </div>
                        <div class="cwv-metric" id="cwv-cls">
                            <div class="cwv-metric-label">CLS</div>
                            <div class="cwv-metric-value">-</div>
                            <div class="cwv-metric-status"></div>
                        </div>
                        <div class="cwv-metric" id="cwv-fcp">
                            <div class="cwv-metric-label">FCP</div>
                            <div class="cwv-metric-value">-</div>
                            <div class="cwv-metric-status"></div>
                        </div>
                        <div class="cwv-metric" id="cwv-ttfb">
                            <div class="cwv-metric-label">TTFB</div>
                            <div class="cwv-metric-value">-</div>
                            <div class="cwv-metric-status"></div>
                        </div>
                        <div class="cwv-metric" id="cwv-inp">
                            <div class="cwv-metric-label">INP</div>
                            <div class="cwv-metric-value">-</div>
                            <div class="cwv-metric-status"></div>
                        </div>
                    </div>
                    <div class="cwv-score">
                        <div class="cwv-score-label">Performance Score</div>
                        <div class="cwv-score-value" id="cwv-score">-</div>
                        <div class="cwv-score-bar">
                            <div class="cwv-score-fill" id="cwv-score-fill"></div>
                        </div>
                    </div>
                    <div class="cwv-details">
                        <div class="cwv-detail-item">
                            <span>Connection:</span>
                            <span id="cwv-connection">-</span>
                        </div>
                        <div class="cwv-detail-item">
                            <span>Device Memory:</span>
                            <span id="cwv-memory">-</span>
                        </div>
                        <div class="cwv-detail-item">
                            <span>Viewport:</span>
                            <span id="cwv-viewport">-</span>
                        </div>
                    </div>
                </div>
            </div>
        `;

        // Add styles
        const styles = document.createElement('style');
        styles.textContent = `
            .cwv-dashboard {
                position: fixed;
                top: 20px;
                right: 20px;
                width: 320px;
                background: rgba(15, 23, 42, 0.95);
                backdrop-filter: blur(10px);
                border: 1px solid rgba(59, 130, 246, 0.3);
                border-radius: 12px;
                color: white;
                font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
                font-size: 12px;
                z-index: 10000;
                box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
                transition: all 0.3s ease;
            }
            
            .cwv-header {
                display: flex;
                justify-content: space-between;
                align-items: center;
                padding: 12px 16px;
                border-bottom: 1px solid rgba(59, 130, 246, 0.2);
                background: rgba(59, 130, 246, 0.1);
                border-radius: 12px 12px 0 0;
            }
            
            .cwv-header h3 {
                margin: 0;
                font-size: 14px;
                font-weight: 600;
                color: #60a5fa;
            }
            
            .cwv-controls {
                display: flex;
                gap: 8px;
            }
            
            .cwv-controls button {
                background: none;
                border: none;
                color: #94a3b8;
                cursor: pointer;
                font-size: 16px;
                padding: 0;
                width: 20px;
                height: 20px;
                display: flex;
                align-items: center;
                justify-content: center;
                border-radius: 4px;
                transition: all 0.2s ease;
            }
            
            .cwv-controls button:hover {
                background: rgba(59, 130, 246, 0.2);
                color: #60a5fa;
            }
            
            .cwv-content {
                padding: 16px;
            }
            
            .cwv-metrics {
                display: grid;
                grid-template-columns: repeat(3, 1fr);
                gap: 12px;
                margin-bottom: 16px;
            }
            
            .cwv-metric {
                text-align: center;
                padding: 8px;
                background: rgba(30, 41, 59, 0.5);
                border-radius: 8px;
                border: 1px solid rgba(71, 85, 105, 0.3);
            }
            
            .cwv-metric-label {
                font-size: 10px;
                color: #94a3b8;
                margin-bottom: 4px;
                font-weight: 500;
            }
            
            .cwv-metric-value {
                font-size: 14px;
                font-weight: 600;
                margin-bottom: 4px;
            }
            
            .cwv-metric-status {
                width: 8px;
                height: 8px;
                border-radius: 50%;
                margin: 0 auto;
                background: #6b7280;
            }
            
            .cwv-metric-status.good { background: #10b981; }
            .cwv-metric-status.needs-improvement { background: #f59e0b; }
            .cwv-metric-status.poor { background: #ef4444; }
            
            .cwv-score {
                margin-bottom: 16px;
                text-align: center;
            }
            
            .cwv-score-label {
                font-size: 11px;
                color: #94a3b8;
                margin-bottom: 8px;
            }
            
            .cwv-score-value {
                font-size: 24px;
                font-weight: 700;
                margin-bottom: 8px;
            }
            
            .cwv-score-bar {
                height: 4px;
                background: rgba(71, 85, 105, 0.3);
                border-radius: 2px;
                overflow: hidden;
            }
            
            .cwv-score-fill {
                height: 100%;
                background: linear-gradient(90deg, #ef4444 0%, #f59e0b 50%, #10b981 100%);
                width: 0%;
                transition: width 0.5s ease;
            }
            
            .cwv-details {
                display: flex;
                flex-direction: column;
                gap: 6px;
            }
            
            .cwv-detail-item {
                display: flex;
                justify-content: space-between;
                font-size: 11px;
                color: #94a3b8;
            }
            
            .cwv-detail-item span:last-child {
                color: #e2e8f0;
                font-weight: 500;
            }
            
            .cwv-dashboard.minimized .cwv-content {
                display: none;
            }
            
            .cwv-dashboard.minimized {
                width: auto;
            }
        `;

        document.head.appendChild(styles);
        document.body.appendChild(dashboard);
        
        // Make it globally accessible
        window.cwvDashboard = this;
    }

    startMonitoring() {
        // Connect to the performance monitor
        if (window.AdvancedPerformanceMonitor) {
            this.connectToPerformanceMonitor();
        }
        
        // Update dashboard every second
        this.updateInterval = setInterval(() => {
            this.updateDashboard();
        }, 1000);
        
        // Initial update
        this.updateDashboard();
    }

    connectToPerformanceMonitor() {
        // Listen for performance updates
        document.addEventListener('performance-metric-updated', (event) => {
            const { metric, value, status } = event.detail;
            this.updateMetric(metric, value, status);
        });
    }

    updateDashboard() {
        // Update connection info
        this.updateConnectionInfo();
        
        // Update device info
        this.updateDeviceInfo();
        
        // Update viewport info
        this.updateViewportInfo();
        
        // Calculate and update performance score
        this.updatePerformanceScore();
    }

    updateMetric(metric, value, status) {
        this.metrics.set(metric, { value, status });
        
        const element = document.getElementById(`cwv-${metric}`);
        if (element) {
            const valueElement = element.querySelector('.cwv-metric-value');
            const statusElement = element.querySelector('.cwv-metric-status');
            
            if (valueElement) {
                valueElement.textContent = this.formatMetricValue(metric, value);
            }
            
            if (statusElement) {
                statusElement.className = `cwv-metric-status ${status}`;
            }
        }
    }

    formatMetricValue(metric, value) {
        if (value === undefined || value === null) return '-';
        
        switch (metric) {
            case 'cls':
                return value.toFixed(3);
            case 'lcp':
            case 'fid':
            case 'fcp':
            case 'ttfb':
            case 'inp':
                return `${Math.round(value)}ms`;
            default:
                return Math.round(value);
        }
    }

    updateConnectionInfo() {
        const connectionElement = document.getElementById('cwv-connection');
        if (connectionElement && 'connection' in navigator) {
            const conn = navigator.connection;
            connectionElement.textContent = `${conn.effectiveType} (${conn.downlink}Mbps)`;
        }
    }

    updateDeviceInfo() {
        const memoryElement = document.getElementById('cwv-memory');
        if (memoryElement) {
            const memory = navigator.deviceMemory || 'Unknown';
            memoryElement.textContent = `${memory}GB`;
        }
    }

    updateViewportInfo() {
        const viewportElement = document.getElementById('cwv-viewport');
        if (viewportElement) {
            viewportElement.textContent = `${window.innerWidth}×${window.innerHeight}`;
        }
    }

    updatePerformanceScore() {
        // Calculate performance score based on available metrics
        let score = 0;
        let count = 0;
        
        this.metrics.forEach(({ status }) => {
            if (status === 'good') score += 100;
            else if (status === 'needs-improvement') score += 50;
            else if (status === 'poor') score += 0;
            count++;
        });
        
        const finalScore = count > 0 ? Math.round(score / count) : 0;
        
        const scoreElement = document.getElementById('cwv-score');
        const fillElement = document.getElementById('cwv-score-fill');
        
        if (scoreElement) {
            scoreElement.textContent = finalScore;
            scoreElement.style.color = this.getScoreColor(finalScore);
        }
        
        if (fillElement) {
            fillElement.style.width = `${finalScore}%`;
        }
    }

    getScoreColor(score) {
        if (score >= 90) return '#10b981';
        if (score >= 50) return '#f59e0b';
        return '#ef4444';
    }

    setupKeyboardShortcut() {
        // Toggle dashboard with Ctrl+Shift+P
        document.addEventListener('keydown', (event) => {
            if (event.ctrlKey && event.shiftKey && event.key === 'P') {
                event.preventDefault();
                this.toggle();
            }
        });
    }

    toggle() {
        const dashboard = document.getElementById('core-web-vitals-dashboard');
        if (dashboard) {
            dashboard.querySelector('.cwv-dashboard').classList.toggle('minimized');
        }
    }

    show() {
        const dashboard = document.getElementById('core-web-vitals-dashboard');
        if (dashboard) {
            dashboard.style.display = 'block';
            this.isVisible = true;
        }
    }

    hide() {
        const dashboard = document.getElementById('core-web-vitals-dashboard');
        if (dashboard) {
            dashboard.style.display = 'none';
            this.isVisible = false;
        }
    }

    destroy() {
        if (this.updateInterval) {
            clearInterval(this.updateInterval);
        }
        
        const dashboard = document.getElementById('core-web-vitals-dashboard');
        if (dashboard) {
            dashboard.remove();
        }
    }
}

// Initialize dashboard
if (typeof window !== 'undefined') {
    document.addEventListener('DOMContentLoaded', () => {
        new CoreWebVitalsDashboard();
    });
}
