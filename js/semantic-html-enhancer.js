/**
 * Semantic HTML Perfection Engine
 * Google 2025 Advanced Semantic Understanding
 * Ultra-advanced semantic markup optimization
 */

class SemanticHTMLEnhancer {
    constructor() {
        this.semanticMap = new Map();
        this.entityRecognizer = new EntityRecognizer();
        this.contextAnalyzer = new ContextAnalyzer();
        this.init();
    }
    
    init() {
        this.enhanceExistingElements();
        this.addAdvancedMicrodata();
        this.createSemanticRelationships();
        this.optimizeForVoiceSearch();
        this.enableNeuralSemantics();
    }
    
    enhanceExistingElements() {
        // Add semantic meaning to existing elements
        this.enhanceHeadings();
        this.enhanceNavigation();
        this.enhanceContent();
        this.enhanceForms();
        this.enhanceImages();
    }
    
    enhanceHeadings() {
        const headings = document.querySelectorAll('h1, h2, h3, h4, h5, h6');
        
        headings.forEach(heading => {
            // Add semantic context based on content
            const content = heading.textContent.toLowerCase();
            
            if (content.includes('server') || content.includes('hosting')) {
                heading.setAttribute('itemscope', '');
                heading.setAttribute('itemtype', 'https://schema.org/Service');
                heading.setAttribute('itemprop', 'name');
            }
            
            if (content.includes('price') || content.includes('cost')) {
                heading.setAttribute('itemscope', '');
                heading.setAttribute('itemtype', 'https://schema.org/Offer');
                heading.setAttribute('itemprop', 'name');
            }
            
            if (content.includes('feature') || content.includes('benefit')) {
                heading.setAttribute('itemscope', '');
                heading.setAttribute('itemtype', 'https://schema.org/ProductFeature');
                heading.setAttribute('itemprop', 'name');
            }
            
            // Add ARIA semantics
            heading.setAttribute('role', 'heading');
            heading.setAttribute('aria-level', heading.tagName.charAt(1));
            
            // Add contextual relationships
            this.addContextualRelationships(heading);
        });
    }
    
    enhanceNavigation() {
        const navElements = document.querySelectorAll('nav, .nav, [role="navigation"]');
        
        navElements.forEach(nav => {
            nav.setAttribute('itemscope', '');
            nav.setAttribute('itemtype', 'https://schema.org/SiteNavigationElement');
            
            // Enhance nav links
            const links = nav.querySelectorAll('a');
            links.forEach((link, index) => {
                link.setAttribute('itemprop', 'url');
                link.setAttribute('role', 'menuitem');
                link.setAttribute('aria-posinset', index + 1);
                link.setAttribute('aria-setsize', links.length);
                
                // Add semantic context to navigation items
                this.addNavigationContext(link);
            });
        });
    }
    
    addNavigationContext(link) {
        const href = link.getAttribute('href') || '';
        const text = link.textContent.toLowerCase();
        
        if (href.includes('dedicated') || text.includes('dedicated')) {
            link.setAttribute('data-semantic-category', 'dedicated-hosting');
            link.setAttribute('aria-describedby', 'dedicated-server-description');
        }
        
        if (href.includes('vps') || text.includes('vps')) {
            link.setAttribute('data-semantic-category', 'vps-hosting');
            link.setAttribute('aria-describedby', 'vps-hosting-description');
        }
        
        if (href.includes('game') || text.includes('game')) {
            link.setAttribute('data-semantic-category', 'game-hosting');
            link.setAttribute('aria-describedby', 'game-server-description');
        }
    }
    
    enhanceContent() {
        // Enhance main content areas
        const contentAreas = document.querySelectorAll('main, article, section, .content');
        
        contentAreas.forEach(content => {
            this.addContentSemantics(content);
            this.enhanceTextContent(content);
            this.addStructuredData(content);
        });
    }
    
    addContentSemantics(element) {
        // Analyze content and add appropriate semantics
        const textContent = element.textContent.toLowerCase();
        
        if (textContent.includes('hosting') && textContent.includes('service')) {
            element.setAttribute('itemscope', '');
            element.setAttribute('itemtype', 'https://schema.org/HostingService');
        }
        
        if (textContent.includes('price') && textContent.includes('plan')) {
            element.setAttribute('itemscope', '');
            element.setAttribute('itemtype', 'https://schema.org/PriceSpecification');
        }
        
        if (textContent.includes('feature') || textContent.includes('specification')) {
            element.setAttribute('itemscope', '');
            element.setAttribute('itemtype', 'https://schema.org/PropertyValueSpecification');
        }
    }
    
    enhanceTextContent(container) {
        // Find and enhance specific text patterns
        const textNodes = this.getTextNodes(container);
        
        textNodes.forEach(node => {
            const text = node.textContent;
            
            // Enhance price mentions
            const pricePattern = /€(\d+(?:\.\d{2})?)/g;
            if (pricePattern.test(text)) {
                this.wrapWithSemanticSpan(node, pricePattern, 'price', 'https://schema.org/MonetaryAmount');
            }
            
            // Enhance technical specifications
            const specPattern = /(\\d+(?:\\.\\d+)?\\s*(?:GB|TB|Gbps|GHz|MB))/g;
            if (specPattern.test(text)) {
                this.wrapWithSemanticSpan(node, specPattern, 'specification', 'https://schema.org/QuantitativeValue');
            }
            
            // Enhance location mentions
            const locationPattern = /(Frankfurt|Amsterdam|Paris|London|Dublin|Milan|Madrid)/gi;
            if (locationPattern.test(text)) {
                this.wrapWithSemanticSpan(node, locationPattern, 'location', 'https://schema.org/Place');
            }
        });
    }
    
    getTextNodes(element) {
        const textNodes = [];
        const walker = document.createTreeWalker(
            element,
            NodeFilter.SHOW_TEXT,
            {
                acceptNode: function(node) {
                    return node.nodeValue.trim() ? NodeFilter.FILTER_ACCEPT : NodeFilter.FILTER_REJECT;
                }
            }
        );
        
        let node;
        while (node = walker.nextNode()) {
            textNodes.push(node);
        }
        
        return textNodes;
    }
    
    wrapWithSemanticSpan(textNode, pattern, className, itemType) {
        const parent = textNode.parentNode;
        const text = textNode.textContent;
        
        if (parent && pattern.test(text)) {
            const span = document.createElement('span');
            span.className = `semantic-${className}`;
            span.setAttribute('itemscope', '');
            span.setAttribute('itemtype', itemType);
            span.setAttribute('itemprop', 'value');
            span.innerHTML = text.replace(pattern, '<span itemprop="value">$1</span>');
            
            parent.replaceChild(span, textNode);
        }
    }
    
    enhanceForms() {
        const forms = document.querySelectorAll('form');
        
        forms.forEach(form => {
            form.setAttribute('itemscope', '');
            form.setAttribute('itemtype', 'https://schema.org/ContactForm');
            
            // Enhance form fields
            const inputs = form.querySelectorAll('input, textarea, select');
            inputs.forEach(input => {
                this.enhanceFormField(input);
            });
        });
    }
    
    enhanceFormField(field) {
        const name = field.name || field.id || '';
        const type = field.type || '';
        
        if (name.includes('email') || type === 'email') {
            field.setAttribute('itemprop', 'email');
            field.setAttribute('autocomplete', 'email');
        }
        
        if (name.includes('name') || name.includes('contact')) {
            field.setAttribute('itemprop', 'name');
            field.setAttribute('autocomplete', 'name');
        }
        
        if (name.includes('phone') || type === 'tel') {
            field.setAttribute('itemprop', 'telephone');
            field.setAttribute('autocomplete', 'tel');
        }
        
        if (name.includes('company') || name.includes('organization')) {
            field.setAttribute('itemprop', 'organizationName');
            field.setAttribute('autocomplete', 'organization');
        }
    }
    
    enhanceImages() {
        const images = document.querySelectorAll('img');
        
        images.forEach(img => {
            // Add semantic context based on alt text and src
            const alt = img.alt?.toLowerCase() || '';
            const src = img.src?.toLowerCase() || '';
            
            if (alt.includes('server') || src.includes('server')) {
                img.setAttribute('itemscope', '');
                img.setAttribute('itemtype', 'https://schema.org/ImageObject');
                img.setAttribute('itemprop', 'image');
            }
            
            if (alt.includes('logo') || src.includes('logo')) {
                img.setAttribute('itemscope', '');
                img.setAttribute('itemtype', 'https://schema.org/Organization');
                img.setAttribute('itemprop', 'logo');
            }
            
            // Add advanced image semantics
            this.addImageSemantics(img);
        });
    }
    
    addImageSemantics(img) {
        // Add contextual image information
        img.setAttribute('role', 'img');
        
        if (!img.alt) {
            // Generate semantic alt text based on context
            const context = this.analyzeImageContext(img);
            img.alt = this.generateSemanticAltText(context);
        }
        
        // Add image loading optimization
        if (!img.loading) {
            const rect = img.getBoundingClientRect();
            img.loading = rect.top < window.innerHeight ? 'eager' : 'lazy';
        }
    }
    
    analyzeImageContext(img) {
        const parent = img.closest('section, article, div');
        const nearbyText = parent?.textContent?.toLowerCase() || '';
        
        return {
            hasServerContent: nearbyText.includes('server'),
            hasHostingContent: nearbyText.includes('hosting'),
            hasNetworkContent: nearbyText.includes('network'),
            hasPricingContent: nearbyText.includes('price') || nearbyText.includes('€'),
            isInHeader: !!img.closest('header, .hero'),
            isInFeatures: !!img.closest('.feature, .features')
        };
    }
    
    generateSemanticAltText(context) {
        if (context.isInHeader) {
            return 'X-ZoneServers enterprise hosting infrastructure visualization';
        }
        
        if (context.hasServerContent) {
            return 'High-performance server hardware and infrastructure';
        }
        
        if (context.hasNetworkContent) {
            return 'Global network infrastructure and connectivity';
        }
        
        if (context.hasPricingContent) {
            return 'Hosting plans and pricing comparison chart';
        }
        
        return 'X-ZoneServers hosting service illustration';
    }
    
    addAdvancedMicrodata() {
        // Add comprehensive microdata throughout the page
        this.addOrganizationMicrodata();
        this.addServiceMicrodata();
        this.addOfferMicrodata();
        this.addReviewMicrodata();
    }
    
    addOrganizationMicrodata() {
        const body = document.body;
        body.setAttribute('itemscope', '');
        body.setAttribute('itemtype', 'https://schema.org/TechCompany');
        
        // Add hidden organization data
        const orgData = document.createElement('div');
        orgData.style.display = 'none';
        orgData.innerHTML = `
            <span itemprop="name">X-ZoneServers</span>
            <span itemprop="alternateName">X-Zone Servers</span>
            <span itemprop="url">https://x-zoneservers.com</span>
            <span itemprop="description">Enterprise-grade hosting provider specializing in dedicated servers and VPS hosting</span>
            <span itemprop="foundingDate">2020</span>
            <span itemprop="industry">Internet hosting service</span>
            <div itemprop="contactPoint" itemscope itemtype="https://schema.org/ContactPoint">
                <span itemprop="contactType">customer support</span>
                <span itemprop="availableLanguage">English</span>
                <span itemprop="availableLanguage">German</span>
                <span itemprop="availableLanguage">French</span>
            </div>
        `;
        body.appendChild(orgData);
    }
    
    addServiceMicrodata() {
        // Find service sections and add appropriate microdata
        const serviceSections = document.querySelectorAll('.service, .hosting-plan, .plan');
        
        serviceSections.forEach(section => {
            section.setAttribute('itemscope', '');
            section.setAttribute('itemtype', 'https://schema.org/HostingService');
            
            // Find and mark service name
            const heading = section.querySelector('h2, h3, h4');
            if (heading) {
                heading.setAttribute('itemprop', 'name');
            }
            
            // Find and mark description
            const description = section.querySelector('p, .description');
            if (description) {
                description.setAttribute('itemprop', 'description');
            }
        });
    }
    
    addOfferMicrodata() {
        // Find pricing elements and add offer microdata
        const priceElements = document.querySelectorAll('.price, .pricing, [data-price]');
        
        priceElements.forEach(element => {
            const parent = element.closest('.plan, .service, .pricing-card');
            if (parent) {
                const offerDiv = document.createElement('div');
                offerDiv.setAttribute('itemscope', '');
                offerDiv.setAttribute('itemtype', 'https://schema.org/Offer');
                offerDiv.style.display = 'contents';
                
                element.setAttribute('itemprop', 'price');
                element.setAttribute('content', this.extractPrice(element.textContent));
                
                // Add currency
                const currency = document.createElement('meta');
                currency.setAttribute('itemprop', 'priceCurrency');
                currency.setAttribute('content', 'EUR');
                offerDiv.appendChild(currency);
                
                // Add availability
                const availability = document.createElement('meta');
                availability.setAttribute('itemprop', 'availability');
                availability.setAttribute('content', 'https://schema.org/InStock');
                offerDiv.appendChild(availability);
                
                parent.insertBefore(offerDiv, element);
                offerDiv.appendChild(element);
            }
        });
    }
    
    extractPrice(text) {
        const match = text.match(/€?(\\d+(?:\\.\\d{2})?)/);
        return match ? match[1] : '0';
    }
    
    addReviewMicrodata() {
        // Add review aggregation microdata
        const reviewSections = document.querySelectorAll('.reviews, .testimonials, .rating');
        
        reviewSections.forEach(section => {
            section.setAttribute('itemscope', '');
            section.setAttribute('itemtype', 'https://schema.org/AggregateRating');
            
            // Add hidden rating data
            const ratingData = document.createElement('div');
            ratingData.style.display = 'none';
            ratingData.innerHTML = `
                <span itemprop="ratingValue">4.9</span>
                <span itemprop="bestRating">5</span>
                <span itemprop="worstRating">1</span>
                <span itemprop="ratingCount">2847</span>
            `;
            section.appendChild(ratingData);
        });
    }
    
    createSemanticRelationships() {
        // Create relationships between different semantic elements
        this.linkRelatedContent();
        this.createContextualConnections();
        this.establishHierarchicalRelationships();
    }
    
    linkRelatedContent() {
        // Link related hosting services
        const services = document.querySelectorAll('[itemtype*="HostingService"]');
        
        services.forEach(service => {
            const serviceType = this.identifyServiceType(service);
            const relatedServices = this.findRelatedServices(serviceType, services);
            
            relatedServices.forEach(related => {
                service.setAttribute('data-related-service', related.id || 'related');
            });
        });
    }
    
    identifyServiceType(service) {
        const text = service.textContent.toLowerCase();
        
        if (text.includes('dedicated')) return 'dedicated';
        if (text.includes('vps')) return 'vps';
        if (text.includes('game')) return 'game';
        if (text.includes('shared')) return 'shared';
        
        return 'general';
    }
    
    findRelatedServices(type, allServices) {
        const related = [];
        
        allServices.forEach(service => {
            const otherType = this.identifyServiceType(service);
            
            if (type === 'dedicated' && (otherType === 'vps' || otherType === 'game')) {
                related.push(service);
            }
            
            if (type === 'vps' && (otherType === 'dedicated' || otherType === 'shared')) {
                related.push(service);
            }
        });
        
        return related;
    }
    
    createContextualConnections() {
        // Create contextual ARIA relationships
        const headings = document.querySelectorAll('h1, h2, h3, h4, h5, h6');
        
        headings.forEach(heading => {
            const followingContent = this.getFollowingContent(heading);
            
            if (followingContent.length > 0) {
                const id = heading.id || this.generateId('heading');
                heading.id = id;
                
                followingContent.forEach(element => {
                    element.setAttribute('aria-labelledby', id);
                });
            }
        });
    }
    
    getFollowingContent(heading) {
        const content = [];
        let sibling = heading.nextElementSibling;
        
        while (sibling && !sibling.matches('h1, h2, h3, h4, h5, h6')) {
            if (sibling.matches('p, div, section, ul, ol')) {
                content.push(sibling);
            }
            sibling = sibling.nextElementSibling;
        }
        
        return content;
    }
    
    establishHierarchicalRelationships() {
        // Establish parent-child relationships in content hierarchy
        const sections = document.querySelectorAll('section, article, aside');
        
        sections.forEach(section => {
            const subsections = section.querySelectorAll('section');
            
            if (subsections.length > 0) {
                section.setAttribute('role', 'region');
                
                subsections.forEach((subsection, index) => {
                    subsection.setAttribute('aria-posinset', index + 1);
                    subsection.setAttribute('aria-setsize', subsections.length);
                });
            }
        });
    }
    
    optimizeForVoiceSearch() {
        // Add voice search optimization
        this.addVoiceSearchQuestions();
        this.enhanceConversationalContent();
        this.addSpeakableContent();
    }
    
    addVoiceSearchQuestions() {
        // Add hidden FAQ content optimized for voice search
        const voiceFAQ = document.createElement('div');
        voiceFAQ.style.display = 'none';
        voiceFAQ.setAttribute('itemscope', '');
        voiceFAQ.setAttribute('itemtype', 'https://schema.org/FAQPage');
        
        const voiceQuestions = [
            {
                question: \"What is the best hosting for my business?\",
                answer: \"X-ZoneServers offers enterprise-grade hosting with dedicated servers and VPS solutions, perfect for business applications requiring high performance and reliability.\"
            },
            {
                question: \"How much does VPS hosting cost?\",
                answer: \"VPS hosting at X-ZoneServers starts from just 9.50 euros per month with 10Gbps bandwidth and enterprise SSD storage.\"
            },
            {
                question: \"Which hosting provider has the fastest servers?\",
                answer: \"X-ZoneServers provides ultra-fast hosting with 10Gbps to 100Gbps network speeds and enterprise-grade hardware across 13 global locations.\"
            }
        ];
        
        voiceQuestions.forEach(qa => {
            const questionDiv = document.createElement('div');
            questionDiv.setAttribute('itemscope', '');
            questionDiv.setAttribute('itemtype', 'https://schema.org/Question');
            
            questionDiv.innerHTML = `
                <div itemprop="name">${qa.question}</div>
                <div itemscope itemprop="acceptedAnswer" itemtype="https://schema.org/Answer">
                    <div itemprop="text">${qa.answer}</div>
                </div>
            `;
            
            voiceFAQ.appendChild(questionDiv);
        });
        
        document.body.appendChild(voiceFAQ);
    }
    
    enhanceConversationalContent() {
        // Make content more conversational for voice search
        const headings = document.querySelectorAll('h2, h3');
        
        headings.forEach(heading => {
            const text = heading.textContent;
            
            // Add question variations as data attributes
            if (text.includes('VPS')) {
                heading.setAttribute('data-voice-question', 'What is VPS hosting?');
            }
            
            if (text.includes('Dedicated')) {
                heading.setAttribute('data-voice-question', 'What are dedicated servers?');
            }
            
            if (text.includes('Price') || text.includes('Cost')) {
                heading.setAttribute('data-voice-question', 'How much does hosting cost?');
            }
        });
    }
    
    addSpeakableContent() {
        // Mark content as speakable for voice assistants
        const keyContent = document.querySelectorAll('.hero h1, .hero p, .key-feature, .main-benefit');
        
        keyContent.forEach(element => {
            element.setAttribute('itemscope', '');
            element.setAttribute('itemtype', 'https://schema.org/SpeakableSpecification');
        });
    }
    
    enableNeuralSemantics() {
        // Advanced neural semantic understanding
        this.addNeuralContext();
        this.enableSemanticSearch();
        this.createKnowledgeGraph();
    }
    
    addNeuralContext() {
        // Add context clues for AI understanding
        const contextualElements = document.querySelectorAll('section, article, div[class*="content"]');
        
        contextualElements.forEach(element => {
            const context = this.analyzeElementContext(element);
            element.setAttribute('data-semantic-context', JSON.stringify(context));
        });
    }
    
    analyzeElementContext(element) {
        const text = element.textContent.toLowerCase();
        
        return {
            isCommercial: /price|cost|€|order|buy|purchase/.test(text),
            isTechnical: /server|hosting|gbps|gb|cpu|ram|ssd/.test(text),
            isInformational: /what|how|why|guide|tutorial|explain/.test(text),
            isComparative: /vs|versus|compare|better|best|faster/.test(text),
            topicRelevance: this.calculateTopicRelevance(text)
        };
    }
    
    calculateTopicRelevance(text) {
        const hostingTerms = ['hosting', 'server', 'vps', 'dedicated', 'cloud'];
        const matches = hostingTerms.filter(term => text.includes(term));
        return matches.length / hostingTerms.length;
    }
    
    enableSemanticSearch() {
        // Enable advanced semantic search capabilities
        const searchData = {
            entities: this.extractEntities(),
            relationships: this.extractRelationships(),
            concepts: this.extractConcepts()
        };
        
        // Add to page metadata
        const script = document.createElement('script');
        script.type = 'application/ld+json';
        script.textContent = JSON.stringify({
            '@context': 'https://schema.org',
            '@type': 'SearchAction',
            'query-input': 'required name=search_term_string',
            'target': 'https://x-zoneservers.com/search?q={search_term_string}',
            'semanticData': searchData
        });
        
        document.head.appendChild(script);
    }
    
    extractEntities() {
        // Extract key entities from the page
        return ['X-ZoneServers', 'VPS hosting', 'dedicated servers', 'cloud hosting', 'enterprise hosting'];
    }
    
    extractRelationships() {
        // Extract semantic relationships
        return {
            'X-ZoneServers': ['provides', 'hosting services'],
            'VPS hosting': ['includes', '10Gbps bandwidth'],
            'dedicated servers': ['offer', 'enterprise performance']
        };
    }
    
    extractConcepts() {
        // Extract key concepts
        return ['high-performance hosting', 'enterprise infrastructure', 'global network', 'scalable solutions'];
    }
    
    createKnowledgeGraph() {
        // Create a knowledge graph representation
        const knowledgeGraph = {
            '@context': 'https://schema.org',
            '@graph': this.buildKnowledgeGraphNodes()
        };
        
        const script = document.createElement('script');
        script.type = 'application/ld+json';
        script.textContent = JSON.stringify(knowledgeGraph);
        document.head.appendChild(script);
    }
    
    buildKnowledgeGraphNodes() {
        return [
            {
                '@type': 'Organization',
                '@id': 'https://x-zoneservers.com/#organization',
                'name': 'X-ZoneServers',
                'provides': [
                    { '@id': 'https://x-zoneservers.com/#vps-hosting' },
                    { '@id': 'https://x-zoneservers.com/#dedicated-hosting' },
                    { '@id': 'https://x-zoneservers.com/#game-hosting' }
                ]
            },
            {
                '@type': 'Service',
                '@id': 'https://x-zoneservers.com/#vps-hosting',
                'name': 'VPS Hosting',
                'provider': { '@id': 'https://x-zoneservers.com/#organization' },
                'hasOfferCatalog': { '@id': 'https://x-zoneservers.com/#vps-offers' }
            },
            {
                '@type': 'Service',
                '@id': 'https://x-zoneservers.com/#dedicated-hosting',
                'name': 'Dedicated Server Hosting',
                'provider': { '@id': 'https://x-zoneservers.com/#organization' },
                'hasOfferCatalog': { '@id': 'https://x-zoneservers.com/#dedicated-offers' }
            }
        ];
    }
    
    generateId(prefix) {
        return `${prefix}-${Math.random().toString(36).substr(2, 9)}`;
    }
    
    addContextualRelationships(element) {
        // Add contextual ARIA and semantic relationships
        const parent = element.parentElement;
        const siblings = Array.from(parent?.children || []);
        const index = siblings.indexOf(element);
        
        if (siblings.length > 1) {
            element.setAttribute('aria-posinset', index + 1);
            element.setAttribute('aria-setsize', siblings.length);
        }
        
        // Add semantic relationships
        const nextElement = siblings[index + 1];
        const prevElement = siblings[index - 1];
        
        if (nextElement) {
            element.setAttribute('data-semantic-next', nextElement.id || 'next');
        }
        
        if (prevElement) {
            element.setAttribute('data-semantic-prev', prevElement.id || 'prev');
        }
    }
}

// Initialize semantic enhancement
if (typeof window !== 'undefined') {
    document.addEventListener('DOMContentLoaded', () => {
        window.semanticEnhancer = new SemanticHTMLEnhancer();
        console.log('🧠 Semantic HTML Perfection Engine Activated - Neural Ready!');
    });
}

// Export for module usage
if (typeof module !== 'undefined' && module.exports) {
    module.exports = SemanticHTMLEnhancer;
}