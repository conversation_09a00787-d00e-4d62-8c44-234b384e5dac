/**
 * X-ZoneServers Ultimate Image SEO & Performance Optimization
 * Google 2025+: Next-generation image optimization for perfect Core Web Vitals
 * Implements cutting-edge techniques for maximum search rankings
 */

class UltimateImageSEOOptimizer {
    constructor() {
        this.imageCache = new Map();
        this.lazyLoadObserver = null;
        this.performanceObserver = null;
        this.webpSupport = null;
        this.avifSupport = null;
        this.intersectionObserver = null;
        this.imageMetrics = new Map();
        this.init();
    }

    init() {
        // Initialize when DOM is ready
        if (document.readyState === 'loading') {
            document.addEventListener('DOMContentLoaded', () => this.setupUltimateImageOptimization());
        } else {
            this.setupUltimateImageOptimization();
        }
    }

    async setupUltimateImageOptimization() {
        // Detect modern image format support
        await this.detectImageFormatSupport();

        // Core optimizations
        this.addAdvancedImageSchemaMarkup();
        this.implementIntelligentLazyLoading();
        this.addCriticalImagePreloading();
        this.optimizeBackgroundImages();
        this.setupImageErrorHandling();
        this.implementImagePerformanceMonitoring();

        // Advanced features
        this.addImageSitemapGeneration();
        this.implementResponsiveImageOptimization();
        this.addImageAccessibilityEnhancements();
        this.setupImageCDNOptimization();
    }

    // Detect support for modern image formats
    async detectImageFormatSupport() {
        // Check WebP support
        this.webpSupport = await this.checkImageFormatSupport('webp');

        // Check AVIF support (next-gen format)
        this.avifSupport = await this.checkImageFormatSupport('avif');

        console.log(`Image format support - WebP: ${this.webpSupport}, AVIF: ${this.avifSupport}`);
    }

    checkImageFormatSupport(format) {
        return new Promise((resolve) => {
            const canvas = document.createElement('canvas');
            canvas.width = 1;
            canvas.height = 1;
            const ctx = canvas.getContext('2d');
            ctx.fillRect(0, 0, 1, 1);

            canvas.toBlob((blob) => {
                resolve(!!blob);
            }, `image/${format}`);
        });
    }

    // Advanced structured data for all images
    addAdvancedImageSchemaMarkup() {
        const imageSchemas = [
            {
                "@context": "https://schema.org",
                "@type": "ImageObject",
                "@id": `${window.location.origin}/#hero-image`,
                "contentUrl": this.getOptimalImageUrl("/images/og-homepage.jpg"),
                "name": "X-ZoneServers Enterprise Hosting Solutions",
                "description": "AI-optimized enterprise hosting infrastructure with quantum-safe security and carbon-neutral datacenters",
                "width": "1200",
                "height": "630",
                "encodingFormat": this.getOptimalImageFormat(),
                "author": {
                    "@type": "Organization",
                    "name": "X-ZoneServers",
                    "url": window.location.origin
                },
                "copyrightHolder": {
                    "@type": "Organization",
                    "name": "X-ZoneServers"
                },
                "license": `${window.location.origin}/terms-and-conditions.html`,
                "acquireLicensePage": `${window.location.origin}/about.html`,
                "creditText": "X-ZoneServers Enterprise Infrastructure",
                "creator": {
                    "@type": "Organization",
                    "name": "X-ZoneServers Design Team"
                },
                "datePublished": "2025-01-15",
                "uploadDate": "2025-01-15",
                "keywords": ["enterprise hosting", "dedicated servers", "VPS hosting", "cloud infrastructure"],
                "representativeOfPage": true,
                "thumbnail": {
                    "@type": "ImageObject",
                    "contentUrl": this.getOptimalImageUrl("/images/og-homepage-thumb.jpg"),
                    "width": "300",
                    "height": "157"
                }
            },
            {
                "@context": "https://schema.org",
                "@type": "ImageGallery",
                "@id": `${window.location.origin}/#infrastructure-gallery`,
                "name": "X-ZoneServers Infrastructure Gallery",
                "description": "Visual showcase of our global datacenter infrastructure and AI-optimized hosting solutions",
                "image": [
                    this.getOptimalImageUrl("/images/datacenter-frankfurt.jpg"),
                    this.getOptimalImageUrl("/images/datacenter-amsterdam.jpg"),
                    this.getOptimalImageUrl("/images/network-infrastructure.jpg"),
                    this.getOptimalImageUrl("/images/server-hardware.jpg")
                ]
            }
        ];

        imageSchemas.forEach(schema => {
            const script = document.createElement('script');
            script.type = 'application/ld+json';
            script.textContent = JSON.stringify(schema);
            document.head.appendChild(script);
        });
    }

    // Get optimal image URL based on format support and device capabilities
    getOptimalImageUrl(originalUrl) {
        if (!originalUrl) return originalUrl;

        const url = new URL(originalUrl, window.location.origin);
        const extension = url.pathname.split('.').pop().toLowerCase();

        // Only optimize common image formats
        if (!['jpg', 'jpeg', 'png', 'webp'].includes(extension)) {
            return originalUrl;
        }

        // Determine optimal format
        let optimalFormat = extension;
        if (this.avifSupport && extension !== 'svg') {
            optimalFormat = 'avif';
        } else if (this.webpSupport && extension !== 'svg') {
            optimalFormat = 'webp';
        }

        // Add responsive sizing parameters
        const devicePixelRatio = window.devicePixelRatio || 1;
        const screenWidth = window.screen.width;

        // Construct optimized URL with CDN parameters
        const optimizedPath = url.pathname.replace(/\.[^.]+$/, `.${optimalFormat}`);
        return `${url.origin}${optimizedPath}?w=${Math.round(screenWidth * devicePixelRatio)}&q=85&format=${optimalFormat}&auto=compress`;
    }

    getOptimalImageFormat() {
        if (this.avifSupport) return 'image/avif';
        if (this.webpSupport) return 'image/webp';
        return 'image/jpeg';
    }

    addImageSitemapGeneration() {
        // Generate image sitemap data for SEO
        const images = document.querySelectorAll('img[src]');
        const imageSitemapData = [];
        
        images.forEach(img => {
            if (img.src && !img.src.startsWith('data:')) {
                imageSitemapData.push({
                    url: img.src,
                    alt: img.alt || '',
                    title: img.title || '',
                    caption: img.getAttribute('data-caption') || ''
                });
            }
        });
        
        // Store image sitemap data for potential use
        if (typeof window !== 'undefined') {
            window.imageSitemapData = imageSitemapData;
        }
    }

    // Implement intelligent lazy loading with performance monitoring
    implementIntelligentLazyLoading() {
        if (!('IntersectionObserver' in window)) {
            // Fallback for older browsers
            this.fallbackLazyLoading();
            return;
        }

        this.intersectionObserver = new IntersectionObserver((entries) => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    const img = entry.target;
                    this.loadImageWithMetrics(img);
                    this.intersectionObserver.unobserve(img);
                }
            });
        }, {
            rootMargin: '100px 0px', // Load images 100px before they come into view
            threshold: 0.01
        });

        // Enhanced image discovery and optimization
        this.discoverAndOptimizeImages();
    }

    discoverAndOptimizeImages() {
        // Find all images that need optimization
        const images = document.querySelectorAll('img');

        images.forEach((img, index) => {
            // Skip if already optimized
            if (img.hasAttribute('data-optimized')) return;

            // Add loading attribute for native lazy loading support
            if (index > 2) { // Don't lazy load first 3 images (above fold)
                img.loading = 'lazy';
            }

            // Add proper alt text if missing
            if (!img.alt && img.src) {
                img.alt = this.generateAltText(img.src);
            }

            // Optimize src for modern formats
            if (img.src && !img.src.includes('data:')) {
                const originalSrc = img.src;
                img.src = this.getOptimalImageUrl(originalSrc);

                // Add srcset for responsive images
                img.srcset = this.generateSrcSet(originalSrc);
                img.sizes = this.generateSizes();
            }

            // Mark as optimized
            img.setAttribute('data-optimized', 'true');

            // Observe for lazy loading if needed
            if (img.hasAttribute('data-src')) {
                this.intersectionObserver.observe(img);
            }
        });
    }

    generateAltText(src) {
        const filename = src.split('/').pop().split('.')[0];
        const words = filename.split(/[-_]/).map(word =>
            word.charAt(0).toUpperCase() + word.slice(1)
        );
        return `X-ZoneServers ${words.join(' ')} - Enterprise Hosting Infrastructure`;
    }

    generateSrcSet(originalUrl) {
        const sizes = [480, 768, 1024, 1200, 1920];
        return sizes.map(size =>
            `${this.getOptimalImageUrl(originalUrl)}&w=${size} ${size}w`
        ).join(', ');
    }

    generateSizes() {
        return '(max-width: 480px) 100vw, (max-width: 768px) 100vw, (max-width: 1024px) 100vw, 1200px';
    }

    loadImageWithMetrics(img) {
        const startTime = performance.now();
        const src = img.dataset.src || img.src;

        if (src) {
            const optimizedSrc = this.getOptimalImageUrl(src);

            img.onload = () => {
                const loadTime = performance.now() - startTime;
                this.recordImageMetrics(src, loadTime, true);
                img.classList.add('loaded');
            };

            img.onerror = () => {
                const loadTime = performance.now() - startTime;
                this.recordImageMetrics(src, loadTime, false);
                this.handleImageError(img);
            };

            img.src = optimizedSrc;
            img.removeAttribute('data-src');
        }
    }

    recordImageMetrics(src, loadTime, success) {
        this.imageMetrics.set(src, {
            loadTime,
            success,
            timestamp: Date.now(),
            format: this.getOptimalImageFormat()
        });

        // Report to analytics if available
        if (typeof gtag !== 'undefined') {
            gtag('event', 'image_load', {
                'custom_parameter_1': src,
                'custom_parameter_2': loadTime,
                'custom_parameter_3': success ? 'success' : 'error'
            });
        }
    }

    handleImageError(img) {
        // Fallback to original format if modern format fails
        const originalSrc = img.dataset.originalSrc || img.src;
        if (originalSrc && originalSrc !== img.src) {
            img.src = originalSrc;
        } else {
            // Use placeholder image
            img.src = 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMzAwIiBoZWlnaHQ9IjIwMCIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj48cmVjdCB3aWR0aD0iMTAwJSIgaGVpZ2h0PSIxMDAlIiBmaWxsPSIjZGRkIi8+PHRleHQgeD0iNTAlIiB5PSI1MCUiIGZvbnQtZmFtaWx5PSJBcmlhbCIgZm9udC1zaXplPSIxNCIgZmlsbD0iIzk5OSIgdGV4dC1hbmNob3I9Im1pZGRsZSIgZHk9Ii4zZW0iPkltYWdlIE5vdCBBdmFpbGFibGU8L3RleHQ+PC9zdmc+';
            img.alt = 'X-ZoneServers - Image not available';
        }
    }

    fallbackLazyLoading() {
        // Simple fallback for browsers without IntersectionObserver
        const images = document.querySelectorAll('img[data-src]');
        images.forEach(img => {
            img.src = img.dataset.src;
            img.removeAttribute('data-src');
        });
    }

    // Add critical image preloading with intelligent prioritization
    addCriticalImagePreloading() {
        const criticalImages = [
            {
                url: '/images/og-homepage.jpg',
                priority: 'high',
                type: 'hero'
            },
            {
                url: '/images/logo.png',
                priority: 'high',
                type: 'logo'
            },
            {
                url: '/images/hero-background.jpg',
                priority: 'high',
                type: 'background'
            }
        ];

        criticalImages.forEach(imageData => {
            const optimizedUrl = this.getOptimalImageUrl(imageData.url);

            // Preload the image
            const link = document.createElement('link');
            link.rel = 'preload';
            link.as = 'image';
            link.href = optimizedUrl;
            link.fetchpriority = imageData.priority;
            link.crossorigin = 'anonymous';
            document.head.appendChild(link);

            // Also preload WebP version if supported
            if (this.webpSupport && !imageData.url.includes('.webp')) {
                const webpLink = document.createElement('link');
                webpLink.rel = 'preload';
                webpLink.as = 'image';
                webpLink.href = imageData.url.replace(/\.(jpg|jpeg|png)$/i, '.webp');
                webpLink.fetchpriority = 'low';
                document.head.appendChild(webpLink);
            }
        });
    }

    // Optimize background images for better performance
    optimizeBackgroundImages() {
        const elementsWithBg = document.querySelectorAll('[style*="background-image"], .hero-bg, .section-bg, .bg-gradient');

        elementsWithBg.forEach(element => {
            const computedStyle = window.getComputedStyle(element);
            const bgImage = computedStyle.backgroundImage;

            if (bgImage && bgImage !== 'none') {
                // Extract URL from background-image
                const urlMatch = bgImage.match(/url\(['"]?([^'"]+)['"]?\)/);
                if (urlMatch) {
                    const originalUrl = urlMatch[1];
                    const optimizedUrl = this.getOptimalImageUrl(originalUrl);

                    // Replace with optimized version
                    element.style.backgroundImage = `url('${optimizedUrl}')`;

                    // Add performance optimizations
                    element.setAttribute('data-bg-optimized', 'true');
                    element.style.contentVisibility = 'auto';
                    element.style.containIntrinsicSize = '1200px 600px';
                }
            }

            // Add accessibility improvements
            if (!element.getAttribute('aria-label') && !element.getAttribute('role')) {
                element.setAttribute('aria-label', 'X-ZoneServers hosting infrastructure background');
            }
        });
    }

    // Setup error handling for images
    setupImageErrorHandling() {
        // Handle background image errors
        document.addEventListener('error', (e) => {
            if (e.target.tagName === 'IMG') {
                this.handleImageError(e.target);
            }
        }, true);
    }

    handleImageError(img) {
        // Fallback to placeholder or retry
        img.src = 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTIwMCIgaGVpZ2h0PSI2MDAiIHZpZXdCb3g9IjAgMCAxMjAwIDYwMCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHJlY3Qgd2lkdGg9IjEyMDAiIGhlaWdodD0iNjAwIiBmaWxsPSIjMUUyOTNCIi8+Cjx0ZXh0IHg9IjYwMCIgeT0iMzAwIiBmaWxsPSIjNjM3NEE4IiB0ZXh0LWFuY2hvcj0ibWlkZGxlIiBkeT0iLjNlbSIgZm9udC1mYW1pbHk9IkludGVyLCBzYW5zLXNlcmlmIiBmb250LXNpemU9IjI0Ij5YLVpvbmVTZXJ2ZXJzPC90ZXh0Pgo8L3N2Zz4K';
        img.alt = 'X-ZoneServers - Image temporarily unavailable';
    }

    // Generate image sitemap data
    generateImageSitemapData() {
        return {
            images: [
                {
                    loc: 'https://x-zoneservers.com/images/og-homepage.jpg',
                    title: 'X-ZoneServers Enterprise Hosting Solutions',
                    caption: 'High-performance dedicated servers and VPS hosting infrastructure with global network coverage'
                },
                {
                    loc: 'https://x-zoneservers.com/images/dedicated-servers.jpg',
                    title: 'Dedicated Streaming Servers',
                    caption: 'Powerful bare metal servers with dual Xeon processors and up to 100Gbps bandwidth'
                },
                {
                    loc: 'https://x-zoneservers.com/images/vps-hosting-premium.jpg',
                    title: 'Premium VPS Hosting Plans',
                    caption: 'KVM VPS hosting with 10Gbps bandwidth, SSD storage, and instant deployment'
                },
                {
                    loc: 'https://x-zoneservers.com/images/network-infrastructure.jpg',
                    title: 'Global Network Infrastructure',
                    caption: 'AI-optimized global network with Tier 1 & Tier 2 peers and premium connectivity'
                },
                {
                    loc: 'https://x-zoneservers.com/images/game-server-hosting.jpg',
                    title: 'Game Server Hosting',
                    caption: 'Ultra-low latency game servers optimized for Minecraft, CS2, Rust, and more'
                }
            ]
        };
    }

    // Add WebP support detection and optimization
    supportsWebP() {
        return new Promise((resolve) => {
            const webP = new Image();
            webP.onload = webP.onerror = () => resolve(webP.height === 2);
            webP.src = 'data:image/webp;base64,UklGRjoAAABXRUJQVlA4IC4AAACyAgCdASoCAAIALmk0mk0iIiIiIgBoSygABc6WWgAA/veff/0PP8bA//LwYAAA';
        });
    }

    // Optimize image loading based on connection speed
    async optimizeForConnection() {
        if ('connection' in navigator) {
            const connection = navigator.connection;
            const slowConnection = connection.effectiveType === 'slow-2g' || connection.effectiveType === '2g';

            if (slowConnection) {
                // Reduce image quality for slow connections
                document.documentElement.classList.add('slow-connection');
            }
        }
    }

    // Add advanced performance monitoring
    implementImagePerformanceMonitoring() {
        if ('PerformanceObserver' in window) {
            this.performanceObserver = new PerformanceObserver((list) => {
                list.getEntries().forEach(entry => {
                    if (entry.initiatorType === 'img') {
                        this.recordImagePerformance(entry);
                    }
                });
            });

            this.performanceObserver.observe({ entryTypes: ['resource'] });
        }
    }

    recordImagePerformance(entry) {
        const metrics = {
            name: entry.name,
            duration: entry.duration,
            transferSize: entry.transferSize,
            encodedBodySize: entry.encodedBodySize,
            decodedBodySize: entry.decodedBodySize,
            timestamp: entry.startTime
        };

        // Store metrics for analysis
        this.imageMetrics.set(entry.name, metrics);

        // Report slow loading images
        if (entry.duration > 1000) {
            console.warn('Slow loading image detected:', entry.name, `${entry.duration}ms`);
        }
    }

    // Add image accessibility enhancements
    addImageAccessibilityEnhancements() {
        const images = document.querySelectorAll('img');

        images.forEach(img => {
            // Ensure all images have alt text
            if (!img.alt) {
                img.alt = this.generateAltText(img.src || 'X-ZoneServers hosting infrastructure');
            }

            // Add role for decorative images
            if (img.alt === '' || img.hasAttribute('data-decorative')) {
                img.setAttribute('role', 'presentation');
                img.setAttribute('aria-hidden', 'true');
            }

            // Add loading state for screen readers
            if (img.loading === 'lazy') {
                img.setAttribute('aria-busy', 'true');

                img.addEventListener('load', () => {
                    img.removeAttribute('aria-busy');
                });
            }
        });
    }

    // Setup image CDN optimization
    setupImageCDNOptimization() {
        // This would integrate with a CDN service like Cloudinary, ImageKit, etc.
        const cdnConfig = {
            baseUrl: 'https://cdn.x-zoneservers.com',
            transformations: {
                quality: 'auto',
                format: 'auto',
                dpr: 'auto'
            }
        };

        // Store CDN config for use in URL generation
        this.cdnConfig = cdnConfig;
    }

    // Get performance metrics for reporting
    getPerformanceMetrics() {
        const metrics = {
            totalImages: this.imageMetrics.size,
            averageLoadTime: 0,
            failedImages: 0,
            formatDistribution: {
                webp: 0,
                avif: 0,
                jpeg: 0,
                png: 0
            }
        };

        let totalLoadTime = 0;

        this.imageMetrics.forEach(metric => {
            if (metric.loadTime) {
                totalLoadTime += metric.loadTime;
            }

            if (!metric.success) {
                metrics.failedImages++;
            }

            // Count format usage
            if (metric.format) {
                const format = metric.format.split('/')[1];
                if (metrics.formatDistribution[format] !== undefined) {
                    metrics.formatDistribution[format]++;
                }
            }
        });

        metrics.averageLoadTime = totalLoadTime / this.imageMetrics.size;

        return metrics;
    }

    // Add missing method to prevent errors
    implementResponsiveImageOptimization() {
        // Implement responsive image optimization
        const images = document.querySelectorAll('img');
        images.forEach(img => {
            if (!img.hasAttribute('loading')) {
                img.setAttribute('loading', 'lazy');
            }
            if (!img.hasAttribute('decoding')) {
                img.setAttribute('decoding', 'async');
            }
        });
    }
}

// Initialize the ultimate image SEO optimizer
if (typeof window !== 'undefined') {
    window.ultimateImageOptimizer = new UltimateImageSEOOptimizer();

    // Expose performance metrics globally
    window.getImagePerformanceMetrics = () => {
        return window.ultimateImageOptimizer.getPerformanceMetrics();
    };
}

// Export for use in other modules
if (typeof module !== 'undefined' && module.exports) {
    module.exports = UltimateImageSEOOptimizer;
}
