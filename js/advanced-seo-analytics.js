/**
 * Advanced SEO Analytics & Monitoring System
 * Comprehensive SEO tracking, performance monitoring, and competitive analysis
 */

class AdvancedSEOAnalytics {
    constructor() {
        this.seoMetrics = new Map();
        this.competitorData = new Map();
        this.rankingSignals = new Map();
        this.searchConsoleData = new Map();
        this.analyticsData = new Map();
        this.init();
    }

    init() {
        this.setupSEOTracking();
        this.monitorRankingSignals();
        this.trackUserBehaviorSignals();
        this.implementCompetitiveAnalysis();
        this.setupSearchConsoleIntegration();
        this.monitorTechnicalSEO();
        this.trackContentPerformance();
        this.setupRealTimeAlerts();
    }

    setupSEOTracking() {
        // Track comprehensive SEO metrics
        const seoData = {
            "@context": "https://schema.org/experimental",
            "@type": "SEOAnalytics",
            "pageMetrics": {
                "titleLength": document.title.length,
                "metaDescriptionLength": this.getMetaDescriptionLength(),
                "headingStructure": this.analyzeHeadingStructure(),
                "internalLinks": this.countInternalLinks(),
                "externalLinks": this.countExternalLinks(),
                "imageOptimization": this.analyzeImageOptimization(),
                "schemaMarkup": this.analyzeSchemaMarkup(),
                "pageSpeed": this.getPageSpeedMetrics(),
                "mobileOptimization": this.analyzeMobileOptimization(),
                "accessibilityScore": this.calculateAccessibilityScore()
            },
            "contentMetrics": {
                "wordCount": this.getWordCount(),
                "readabilityScore": this.calculateReadabilityScore(),
                "keywordDensity": this.analyzeKeywordDensity(),
                "contentFreshness": this.analyzeContentFreshness(),
                "topicalRelevance": this.calculateTopicalRelevance(),
                "expertiseSignals": this.analyzeExpertiseSignals(),
                "trustSignals": this.analyzeTrustSignals()
            },
            "technicalMetrics": {
                "crawlability": this.analyzeCrawlability(),
                "indexability": this.analyzeIndexability(),
                "canonicalization": this.analyzeCanonicals(),
                "hreflangImplementation": this.analyzeHreflang(),
                "structuredDataValidation": this.validateStructuredData(),
                "sitemapOptimization": this.analyzeSitemap(),
                "robotsTxtOptimization": this.analyzeRobotsTxt()
            }
        };

        this.seoMetrics.set('pageAnalysis', seoData);
        this.reportSEOMetrics(seoData);
    }

    getMetaDescriptionLength() {
        const metaDesc = document.querySelector('meta[name="description"]');
        return metaDesc ? metaDesc.content.length : 0;
    }

    analyzeHeadingStructure() {
        const headings = document.querySelectorAll('h1, h2, h3, h4, h5, h6');
        const structure = {};

        headings.forEach(heading => {
            const tag = heading.tagName.toLowerCase();
            structure[tag] = (structure[tag] || 0) + 1;
        });

        return {
            structure,
            h1Count: structure.h1 || 0,
            totalHeadings: headings.length,
            hierarchyScore: this.calculateHierarchyScore(structure)
        };
    }

    calculateHierarchyScore(structure) {
        let score = 1.0;

        // Penalize multiple H1s
        if (structure.h1 > 1) score -= 0.2;

        // Reward proper hierarchy
        if (structure.h1 && structure.h2) score += 0.1;
        if (structure.h2 && structure.h3) score += 0.1;

        return Math.max(0, Math.min(1, score));
    }

    getPageSpeedMetrics() {
        // Simple performance metrics
        const navigation = performance.navigation || {};
        const timing = performance.timing || {};

        return {
            loadTime: timing.loadEventEnd ? timing.loadEventEnd - timing.navigationStart : 0,
            domContentLoaded: timing.domContentLoadedEventEnd ? timing.domContentLoadedEventEnd - timing.navigationStart : 0,
            firstContentfulPaint: 0, // Would require Paint Timing API
            score: 'unknown' // Would require Lighthouse API
        };
    }

    countInternalLinks() {
        const links = document.querySelectorAll('a[href]');
        let internalCount = 0;

        links.forEach(link => {
            const href = link.getAttribute('href');
            if (href && (href.startsWith('/') || href.includes(window.location.hostname))) {
                internalCount++;
            }
        });

        return internalCount;
    }

    countExternalLinks() {
        const links = document.querySelectorAll('a[href]');
        let externalCount = 0;

        links.forEach(link => {
            const href = link.getAttribute('href');
            if (href && href.startsWith('http') && !href.includes(window.location.hostname)) {
                externalCount++;
            }
        });

        return externalCount;
    }

    analyzeImageOptimization() {
        const images = document.querySelectorAll('img');
        let optimizedCount = 0;
        let totalImages = images.length;

        images.forEach(img => {
            let score = 0;

            // Check for alt text
            if (img.alt && img.alt.trim()) score += 0.3;

            // Check for modern formats
            if (img.src && (img.src.includes('.webp') || img.src.includes('.avif'))) score += 0.3;

            // Check for lazy loading
            if (img.loading === 'lazy' || img.dataset.src) score += 0.2;

            // Check for responsive images
            if (img.srcset || img.sizes) score += 0.2;

            if (score >= 0.7) optimizedCount++;
        });

        return {
            totalImages,
            optimizedImages: optimizedCount,
            optimizationRate: totalImages > 0 ? optimizedCount / totalImages : 1,
            missingAltCount: Array.from(images).filter(img => !img.alt || !img.alt.trim()).length
        };
    }

    analyzeSchemaMarkup() {
        const jsonLdScripts = document.querySelectorAll('script[type="application/ld+json"]');
        const schemas = [];

        jsonLdScripts.forEach(script => {
            try {
                const data = JSON.parse(script.textContent);
                if (data['@type']) {
                    schemas.push(data['@type']);
                }
            } catch (e) {
                // Invalid JSON
            }
        });

        return {
            schemaCount: jsonLdScripts.length,
            schemaTypes: [...new Set(schemas)],
            hasOrganization: schemas.includes('Organization'),
            hasWebsite: schemas.includes('WebSite'),
            hasBreadcrumb: schemas.includes('BreadcrumbList'),
            hasProduct: schemas.includes('Product'),
            hasFAQ: schemas.includes('FAQPage')
        };
    }

    monitorRankingSignals() {
        // Monitor Google's ranking signals
        const rankingSignals = {
            "@context": "https://schema.org/experimental",
            "@type": "RankingSignals",
            "coreWebVitals": this.getCoreWebVitalsScore(),
            "mobileUsability": this.getMobileUsabilityScore(),
            "pageExperience": this.getPageExperienceScore(),
            "contentQuality": this.getContentQualityScore(),
            "expertiseAuthority": this.getEATScore(),
            "technicalSEO": this.getTechnicalSEOScore(),
            "userEngagement": this.getUserEngagementScore(),
            "freshness": this.getFreshnessScore(),
            "relevance": this.getRelevanceScore(),
            "trustworthiness": this.getTrustworthinessScore()
        };

        this.rankingSignals.set('current', rankingSignals);
        this.trackRankingChanges(rankingSignals);
    }

    getCoreWebVitalsScore() {
        // Get Core Web Vitals from performance monitor
        if (window.AdvancedPerformanceMonitor) {
            const metrics = window.AdvancedPerformanceMonitor.metrics || {};
            return this.calculateCWVScore(metrics);
        }
        return 0.5; // Default neutral score
    }

    calculateCWVScore(metrics) {
        const scores = [];

        // LCP Score
        if (metrics.lcp) {
            if (metrics.lcp <= 2500) scores.push(1);
            else if (metrics.lcp <= 4000) scores.push(0.5);
            else scores.push(0);
        }

        // FID Score
        if (metrics.fid) {
            if (metrics.fid <= 100) scores.push(1);
            else if (metrics.fid <= 300) scores.push(0.5);
            else scores.push(0);
        }

        // CLS Score
        if (metrics.cls) {
            if (metrics.cls <= 0.1) scores.push(1);
            else if (metrics.cls <= 0.25) scores.push(0.5);
            else scores.push(0);
        }

        return scores.length > 0 ? scores.reduce((a, b) => a + b) / scores.length : 0.5;
    }

    trackUserBehaviorSignals() {
        // Track user behavior signals that impact SEO
        const behaviorSignals = {
            sessionDuration: this.trackSessionDuration(),
            bounceRate: this.calculateBounceRate(),
            pageViews: this.trackPageViews(),
            scrollDepth: this.trackScrollDepth(),
            clickThroughRate: this.calculateCTR(),
            returnVisitorRate: this.calculateReturnVisitorRate(),
            socialSignals: this.trackSocialSignals(),
            brandSearches: this.trackBrandSearches()
        };

        this.analyticsData.set('userBehavior', behaviorSignals);
        this.reportUserBehavior(behaviorSignals);
    }

    implementCompetitiveAnalysis() {
        // Analyze competitor performance (simplified version)
        const competitorAnalysis = {
            "@context": "https://schema.org/experimental",
            "@type": "CompetitiveAnalysis",
            "competitors": this.identifyCompetitors(),
            "keywordGaps": this.analyzeKeywordGaps(),
            "contentGaps": this.analyzeContentGaps(),
            "technicalAdvantages": this.analyzeTechnicalAdvantages(),
            "backlinksComparison": this.analyzeBacklinksComparison(),
            "socialMediaPresence": this.analyzeSocialMediaPresence(),
            "marketShare": this.estimateMarketShare()
        };

        this.competitorData.set('analysis', competitorAnalysis);
    }

    setupSearchConsoleIntegration() {
        // Simulate Search Console data integration
        const searchConsoleData = {
            impressions: this.getSearchImpressions(),
            clicks: this.getSearchClicks(),
            averagePosition: this.getAveragePosition(),
            clickThroughRate: this.getSearchCTR(),
            topQueries: this.getTopQueries(),
            topPages: this.getTopPages(),
            indexingStatus: this.getIndexingStatus(),
            mobileFriendliness: this.getMobileFriendliness(),
            coreWebVitalsReport: this.getCoreWebVitalsReport()
        };

        this.searchConsoleData.set('current', searchConsoleData);
    }

    monitorTechnicalSEO() {
        // Monitor technical SEO factors
        const technicalSEO = {
            siteSpeed: this.analyzeSiteSpeed(),
            mobileOptimization: this.analyzeMobileOptimization(),
            crawlErrors: this.detectCrawlErrors(),
            duplicateContent: this.detectDuplicateContent(),
            brokenLinks: this.detectBrokenLinks(),
            xmlSitemap: this.validateXMLSitemap(),
            robotsTxt: this.validateRobotsTxt(),
            httpsImplementation: this.validateHTTPS(),
            canonicalTags: this.validateCanonicalTags(),
            metaTags: this.validateMetaTags()
        };

        this.seoMetrics.set('technical', technicalSEO);
    }

    trackContentPerformance() {
        // Track content performance metrics
        const contentPerformance = {
            engagementRate: this.calculateEngagementRate(),
            shareability: this.calculateShareability(),
            readabilityScore: this.calculateReadabilityScore(),
            topicalAuthority: this.calculateTopicalAuthority(),
            contentFreshness: this.analyzeContentFreshness(),
            keywordPerformance: this.analyzeKeywordPerformance(),
            contentLength: this.analyzeContentLength(),
            multimediaUsage: this.analyzeMultimediaUsage()
        };

        this.seoMetrics.set('content', contentPerformance);
    }

    setupRealTimeAlerts() {
        // Setup real-time SEO alerts
        const alertThresholds = {
            coreWebVitals: { lcp: 4000, fid: 300, cls: 0.25 },
            pageSpeed: 5000,
            crawlErrors: 5,
            rankingDrop: 10,
            trafficDrop: 0.2
        };

        this.monitorAlerts(alertThresholds);
    }

    // Utility methods for calculations
    getWordCount() {
        const text = document.body.textContent || '';
        return text.trim().split(/\s+/).length;
    }

    calculateReadabilityScore() {
        const text = document.body.textContent || '';
        const sentences = text.split(/[.!?]+/).length;
        const words = text.split(/\s+/).length;
        const avgWordsPerSentence = words / sentences;

        // Simplified Flesch Reading Ease
        let score = 206.835 - (1.015 * avgWordsPerSentence);
        return Math.max(0, Math.min(100, score)) / 100;
    }

    // Reporting methods
    reportSEOMetrics(data) {
        if (typeof gtag !== 'undefined') {
            gtag('event', 'seo_analysis', {
                'page_metrics': data.pageMetrics,
                'content_metrics': data.contentMetrics,
                'technical_metrics': data.technicalMetrics
            });
        }
    }

    reportUserBehavior(data) {
        if (typeof gtag !== 'undefined') {
            gtag('event', 'user_behavior_analysis', data);
        }
    }

    // Generate comprehensive SEO report
    generateSEOReport() {
        return {
            timestamp: new Date().toISOString(),
            url: window.location.href,
            seoMetrics: Object.fromEntries(this.seoMetrics),
            rankingSignals: Object.fromEntries(this.rankingSignals),
            competitorData: Object.fromEntries(this.competitorData),
            searchConsoleData: Object.fromEntries(this.searchConsoleData),
            analyticsData: Object.fromEntries(this.analyticsData),
            overallScore: this.calculateOverallSEOScore(),
            recommendations: this.generateRecommendations()
        };
    }

    calculateOverallSEOScore() {
        // Calculate weighted overall SEO score
        const weights = {
            technical: 0.25,
            content: 0.25,
            performance: 0.20,
            userExperience: 0.15,
            authority: 0.15
        };

        let totalScore = 0;
        let totalWeight = 0;

        // Add scoring logic here based on collected metrics
        // This is a simplified version
        Object.entries(weights).forEach(([category, weight]) => {
            const categoryScore = this.getCategoryScore(category);
            totalScore += categoryScore * weight;
            totalWeight += weight;
        });

        return totalWeight > 0 ? totalScore / totalWeight : 0;
    }

    getCategoryScore(category) {
        // Return category-specific scores
        switch (category) {
            case 'technical':
                return this.getTechnicalSEOScore();
            case 'content':
                return this.getContentQualityScore();
            case 'performance':
                return this.getCoreWebVitalsScore();
            case 'userExperience':
                return this.getUserEngagementScore();
            case 'authority':
                return this.getEATScore();
            default:
                return 0.5;
        }
    }

    generateRecommendations() {
        const recommendations = [];

        // Add recommendations based on analysis
        const seoData = this.seoMetrics.get('pageAnalysis');
        if (seoData) {
            if (seoData.pageMetrics.titleLength > 60) {
                recommendations.push({
                    type: 'title',
                    priority: 'high',
                    message: 'Title tag is too long. Consider shortening to under 60 characters.'
                });
            }

            if (seoData.pageMetrics.metaDescriptionLength === 0) {
                recommendations.push({
                    type: 'meta-description',
                    priority: 'high',
                    message: 'Missing meta description. Add a compelling 150-160 character description.'
                });
            }

            if (seoData.pageMetrics.headingStructure.h1Count === 0) {
                recommendations.push({
                    type: 'heading',
                    priority: 'medium',
                    message: 'Missing H1 tag. Add a descriptive H1 heading to your page.'
                });
            }
        }

        return recommendations;
    }

    // Placeholder methods for complex calculations
    getTechnicalSEOScore() { return 0.85; }
    getContentQualityScore() { return 0.78; }
    getUserEngagementScore() { return 0.72; }
    getEATScore() { return 0.88; }
    getMobileUsabilityScore() { return 0.92; }
    getPageExperienceScore() { return 0.81; }
    getFreshnessScore() { return 0.75; }
    getRelevanceScore() { return 0.83; }
    getTrustworthinessScore() { return 0.89; }

    // Add missing method to prevent errors
    analyzeMobileOptimization() {
        const mobileOptimization = {
            viewport: document.querySelector('meta[name="viewport"]') ? 1 : 0,
            responsiveDesign: window.innerWidth < 768 ? 1 : 0,
            touchFriendly: 'ontouchstart' in window ? 1 : 0,
            mobileSpeed: performance.now() < 3000 ? 1 : 0.5
        };

        const score = Object.values(mobileOptimization).reduce((a, b) => a + b, 0) / 4;
        return {
            score: score,
            details: mobileOptimization,
            recommendations: score < 0.8 ? ['Improve mobile responsiveness', 'Optimize for touch'] : []
        };
    }

    // Add missing method to prevent errors
    calculateAccessibilityScore() {
        const accessibilityChecks = {
            altText: document.querySelectorAll('img[alt]').length / Math.max(document.querySelectorAll('img').length, 1),
            headingStructure: document.querySelectorAll('h1, h2, h3, h4, h5, h6').length > 0 ? 1 : 0,
            skipLinks: document.querySelectorAll('.skip-link, [href="#main-content"]').length > 0 ? 1 : 0,
            ariaLabels: document.querySelectorAll('[aria-label], [aria-labelledby]').length > 0 ? 1 : 0,
            focusable: document.querySelectorAll('button, a, input, select, textarea').length > 0 ? 1 : 0
        };

        const score = Object.values(accessibilityChecks).reduce((a, b) => a + b, 0) / 5;
        return {
            score: score,
            details: accessibilityChecks,
            recommendations: score < 0.8 ? ['Add alt text to images', 'Improve heading structure', 'Add skip links'] : []
        };
    }

    // Provide class method wrapper so callers can use this.analyzeKeywordDensity()
    analyzeKeywordDensity() {
        if (typeof window !== 'undefined' && typeof window.analyzeKeywordDensity === 'function') {
            return window.analyzeKeywordDensity();
        }
        // Graceful fallback
        const text = document.body.innerText.toLowerCase();
        const words = text.split(/\s+/).filter(w => w.length > 3);
        const wordCount = words.length;
        const freq = {};
        words.forEach(w => { freq[w] = (freq[w] || 0) + 1; });
        const top = Object.entries(freq).sort(([,a],[,b]) => b - a).slice(0, 10).map(([word, count]) => ({ word, count, density: wordCount ? ((count/wordCount)*100).toFixed(2) : '0.00' }));
        return { totalWords: wordCount, topKeywords: top };
    }

}

// Initialize Advanced SEO Analytics
if (typeof window !== 'undefined') {
    window.seoAnalytics = new AdvancedSEOAnalytics();

    // Expose for debugging
    window.getSEOReport = () => {
        return window.seoAnalytics.generateSEOReport();
    };

    // Auto-generate report every 5 minutes in development
    if (window.location.hostname === 'localhost' || window.location.hostname.includes('dev')) {
        setInterval(() => {
            console.log('📊 SEO Analytics Report:', window.getSEOReport());
        }, 300000);
    }

    // Standalone utility function for keyword analysis
    function analyzeKeywordDensity() {
        const text = document.body.innerText.toLowerCase();
        const words = text.split(/\s+/).filter(word => word.length > 3);
        const wordCount = words.length;
        const wordFreq = {};

        words.forEach(word => {
            wordFreq[word] = (wordFreq[word] || 0) + 1;
        });

        const sortedWords = Object.entries(wordFreq)
            .sort(([,a], [,b]) => b - a)
            .slice(0, 10);

        return {
            totalWords: wordCount,
            topKeywords: sortedWords.map(([word, count]) => ({
                word,
                count,
                density: ((count / wordCount) * 100).toFixed(2)
            }))
        };
    }
}
