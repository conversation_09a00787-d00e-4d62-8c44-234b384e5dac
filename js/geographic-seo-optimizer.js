/**
 * Geographic SEO Optimizer
 * Advanced location-based SEO optimization for global datacenter network
 */

class GeographicSEOOptimizer {
    constructor() {
        this.datacenters = new Map();
        this.geoTargeting = new Map();
        this.localSEOSignals = new Map();
        this.init();
    }

    init() {
        this.setupDatacenterLocations();
        this.implementGeoTargeting();
        this.addLocalBusinessSignals();
        this.setupHreflangOptimization();
        this.implementLocationBasedContent();
        this.addGeographicStructuredData();
        this.setupLocalSearchOptimization();
    }

    setupDatacenterLocations() {
        // Comprehensive datacenter network with precise geographic data
        const locations = [
            {
                code: 'AMS',
                name: 'Amsterdam',
                country: 'Netherlands',
                region: 'Europe',
                coordinates: { lat: 52.3676, lng: 4.9041 },
                timezone: 'Europe/Amsterdam',
                languages: ['en', 'nl', 'de'],
                currency: 'EUR',
                compliance: ['GDPR', 'EU'],
                networkLatency: {
                    'London': 8,
                    'Frankfurt': 12,
                    'Paris': 15,
                    'Berlin': 18,
                    'Brussels': 10
                },
                internetExchanges: ['AMS-IX', 'NL-ix'],
                tier: 'IV',
                connectivity: '100Gbps+'
            },
            {
                code: 'FRA',
                name: 'Frankfurt',
                country: 'Germany',
                region: 'Europe',
                coordinates: { lat: 50.1109, lng: 8.6821 },
                timezone: 'Europe/Berlin',
                languages: ['en', 'de', 'fr'],
                currency: 'EUR',
                compliance: ['GDPR', 'EU', 'BSI'],
                networkLatency: {
                    'Amsterdam': 12,
                    'London': 15,
                    'Paris': 18,
                    'Zurich': 8,
                    'Vienna': 12
                },
                internetExchanges: ['DE-CIX', 'ECIX'],
                tier: 'IV',
                connectivity: '100Gbps+'
            },
            {
                code: 'LON',
                name: 'London',
                country: 'United Kingdom',
                region: 'Europe',
                coordinates: { lat: 51.5074, lng: -0.1278 },
                timezone: 'Europe/London',
                languages: ['en'],
                currency: 'GBP',
                compliance: ['UK-GDPR', 'ISO27001'],
                networkLatency: {
                    'Amsterdam': 8,
                    'Frankfurt': 15,
                    'Paris': 12,
                    'Dublin': 5,
                    'Edinburgh': 8
                },
                internetExchanges: ['LINX', 'IXLeeds'],
                tier: 'IV',
                connectivity: '100Gbps+'
            },
            {
                code: 'NYC',
                name: 'New York',
                country: 'United States',
                region: 'North America',
                coordinates: { lat: 40.7128, lng: -74.0060 },
                timezone: 'America/New_York',
                languages: ['en', 'es'],
                currency: 'USD',
                compliance: ['SOC2', 'HIPAA', 'PCI-DSS'],
                networkLatency: {
                    'Washington': 5,
                    'Boston': 8,
                    'Chicago': 12,
                    'Atlanta': 15,
                    'Miami': 18
                },
                internetExchanges: ['NYIIX', 'DE-CIX New York'],
                tier: 'IV',
                connectivity: '100Gbps+'
            },
            {
                code: 'LAX',
                name: 'Los Angeles',
                country: 'United States',
                region: 'North America',
                coordinates: { lat: 34.0522, lng: -118.2437 },
                timezone: 'America/Los_Angeles',
                languages: ['en', 'es'],
                currency: 'USD',
                compliance: ['SOC2', 'HIPAA', 'PCI-DSS'],
                networkLatency: {
                    'San Francisco': 8,
                    'Las Vegas': 5,
                    'Phoenix': 10,
                    'Seattle': 15,
                    'Denver': 18
                },
                internetExchanges: ['Any2 Exchange', 'LAIIX'],
                tier: 'IV',
                connectivity: '100Gbps+'
            },
            {
                code: 'SIN',
                name: 'Singapore',
                country: 'Singapore',
                region: 'Asia Pacific',
                coordinates: { lat: 1.3521, lng: 103.8198 },
                timezone: 'Asia/Singapore',
                languages: ['en', 'zh', 'ms'],
                currency: 'SGD',
                compliance: ['PDPA', 'MAS', 'CSA'],
                networkLatency: {
                    'Hong Kong': 12,
                    'Tokyo': 18,
                    'Sydney': 25,
                    'Mumbai': 35,
                    'Bangkok': 8
                },
                internetExchanges: ['SGIX', 'Equinix Singapore'],
                tier: 'IV',
                connectivity: '100Gbps+'
            },
            {
                code: 'TOK',
                name: 'Tokyo',
                country: 'Japan',
                region: 'Asia Pacific',
                coordinates: { lat: 35.6762, lng: 139.6503 },
                timezone: 'Asia/Tokyo',
                languages: ['en', 'ja'],
                currency: 'JPY',
                compliance: ['APPI', 'ISMS', 'SOC2'],
                networkLatency: {
                    'Singapore': 18,
                    'Hong Kong': 15,
                    'Seoul': 8,
                    'Sydney': 22,
                    'Osaka': 5
                },
                internetExchanges: ['JPIX', 'JPNAP'],
                tier: 'IV',
                connectivity: '100Gbps+'
            },
            {
                code: 'SYD',
                name: 'Sydney',
                country: 'Australia',
                region: 'Asia Pacific',
                coordinates: { lat: -33.8688, lng: 151.2093 },
                timezone: 'Australia/Sydney',
                languages: ['en'],
                currency: 'AUD',
                compliance: ['Privacy Act', 'ACSC', 'ISO27001'],
                networkLatency: {
                    'Singapore': 25,
                    'Tokyo': 22,
                    'Hong Kong': 28,
                    'Melbourne': 5,
                    'Perth': 35
                },
                internetExchanges: ['IX Australia', 'PIPE Networks'],
                tier: 'IV',
                connectivity: '100Gbps+'
            }
        ];

        locations.forEach(location => {
            this.datacenters.set(location.code, location);
        });
    }

    implementGeoTargeting() {
        // Use IP-based geolocation only (no browser permission required)
        this.implementIPGeolocation();
    }

    implementLocationBasedContent() {
        // Add location-based content optimization
        const userAgent = navigator.userAgent;
        const language = navigator.language;
        
        // Simple location-based content enhancement
        const locationContent = {
            language: language,
            userAgent: userAgent.substring(0, 100), // Truncate for privacy
            timestamp: Date.now(),
            timezone: Intl.DateTimeFormat().resolvedOptions().timeZone
        };
        
        // Store for potential content optimization
        if (typeof sessionStorage !== 'undefined') {
            sessionStorage.setItem('locationBasedContent', JSON.stringify(locationContent));
        }
    }

    optimizeForUserLocation(lat, lng) {
        // Find nearest datacenter
        const nearestDC = this.findNearestDatacenter(lat, lng);
        
        // Add geographic targeting signals
        const geoSignals = {
            "@context": "https://schema.org/experimental",
            "@type": "GeographicTargeting",
            "userLocation": {
                "latitude": lat,
                "longitude": lng
            },
            "recommendedDatacenter": nearestDC.code,
            "estimatedLatency": this.calculateLatency(lat, lng, nearestDC),
            "localizedContent": true,
            "geoOptimized": true
        };

        this.geoTargeting.set('userLocation', geoSignals);
        this.addGeoTargetingToPage(nearestDC);
    }

    findNearestDatacenter(userLat, userLng) {
        let nearestDC = null;
        let minDistance = Infinity;

        this.datacenters.forEach(dc => {
            const distance = this.calculateDistance(
                userLat, userLng,
                dc.coordinates.lat, dc.coordinates.lng
            );
            
            if (distance < minDistance) {
                minDistance = distance;
                nearestDC = dc;
            }
        });

        return nearestDC;
    }

    calculateDistance(lat1, lng1, lat2, lng2) {
        // Haversine formula for calculating distance between coordinates
        const R = 6371; // Earth's radius in kilometers
        const dLat = this.toRadians(lat2 - lat1);
        const dLng = this.toRadians(lng2 - lng1);
        
        const a = Math.sin(dLat/2) * Math.sin(dLat/2) +
                  Math.cos(this.toRadians(lat1)) * Math.cos(this.toRadians(lat2)) *
                  Math.sin(dLng/2) * Math.sin(dLng/2);
        
        const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1-a));
        return R * c;
    }

    toRadians(degrees) {
        return degrees * (Math.PI / 180);
    }

    calculateLatency(userLat, userLng, datacenter) {
        const distance = this.calculateDistance(
            userLat, userLng,
            datacenter.coordinates.lat, datacenter.coordinates.lng
        );
        
        // Rough latency estimation: ~0.1ms per 10km + base latency
        return Math.round((distance / 100) + 5);
    }

    addGeoTargetingToPage(nearestDC) {
        // Add geo-targeting meta tags
        const geoMeta = document.createElement('meta');
        geoMeta.name = 'nearest-datacenter';
        geoMeta.content = nearestDC.code;
        document.head.appendChild(geoMeta);

        // Add recommended datacenter info
        const dcMeta = document.createElement('meta');
        dcMeta.name = 'recommended-location';
        dcMeta.content = `${nearestDC.name}, ${nearestDC.country}`;
        document.head.appendChild(dcMeta);

        // Show location-specific content
        this.showLocationSpecificContent(nearestDC);
    }

    showLocationSpecificContent(datacenter) {
        // Create location-specific banner or notification
        const locationBanner = document.createElement('div');
        locationBanner.className = 'geo-targeting-banner';
        locationBanner.innerHTML = `
            <div class="bg-blue-600/10 border border-blue-500/30 rounded-lg p-4 mb-6">
                <div class="flex items-center gap-3">
                    <svg class="w-5 h-5 text-blue-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z"></path>
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 11a3 3 0 11-6 0 3 3 0 016 0z"></path>
                    </svg>
                    <div>
                        <div class="text-blue-400 font-medium">Optimized for your location</div>
                        <div class="text-gray-300 text-sm">
                            Our ${datacenter.name} datacenter provides optimal performance for your region
                        </div>
                    </div>
                    <a href="/locations/${datacenter.code.toLowerCase()}-datacenter.html" 
                       class="ml-auto bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg text-sm transition-colors">
                        View Details
                    </a>
                </div>
            </div>
        `;

        // Insert after header or at top of main content
        const mainContent = document.querySelector('main') || document.querySelector('.container');
        if (mainContent) {
            mainContent.insertBefore(locationBanner, mainContent.firstChild);
        }
    }

    implementIPGeolocation() {
        // Fallback IP-based geolocation using a service
        fetch('https://ipapi.co/json/')
            .then(response => response.json())
            .then(data => {
                if (data.latitude && data.longitude) {
                    this.optimizeForUserLocation(data.latitude, data.longitude);
                }
                
                // Add country-specific optimizations
                this.addCountrySpecificOptimizations(data.country_code);
            })
            .catch(() => {
                // Silent fallback - no geolocation available
                console.log('Geolocation not available');
            });
    }

    addCountrySpecificOptimizations(countryCode) {
        // Add country-specific meta tags and optimizations
        const countryMeta = document.createElement('meta');
        countryMeta.name = 'user-country';
        countryMeta.content = countryCode;
        document.head.appendChild(countryMeta);

        // Add country-specific structured data
        const countryData = {
            "@context": "https://schema.org",
            "@type": "Country",
            "identifier": countryCode,
            "name": this.getCountryName(countryCode)
        };

        const script = document.createElement('script');
        script.type = 'application/ld+json';
        script.textContent = JSON.stringify(countryData);
        document.head.appendChild(script);
    }

    getCountryName(countryCode) {
        const countries = {
            'US': 'United States',
            'GB': 'United Kingdom',
            'DE': 'Germany',
            'NL': 'Netherlands',
            'FR': 'France',
            'SG': 'Singapore',
            'JP': 'Japan',
            'AU': 'Australia'
        };
        return countries[countryCode] || countryCode;
    }

    addLocalBusinessSignals() {
        // Add local business signals for each datacenter location
        this.datacenters.forEach(dc => {
            const localSignals = {
                "@context": "https://schema.org",
                "@type": "LocalBusiness",
                "@id": `https://x-zoneservers.com/locations/${dc.code.toLowerCase()}-datacenter.html#business`,
                "name": `X-ZoneServers ${dc.name} Datacenter`,
                "address": {
                    "@type": "PostalAddress",
                    "addressLocality": dc.name,
                    "addressCountry": dc.country
                },
                "geo": {
                    "@type": "GeoCoordinates",
                    "latitude": dc.coordinates.lat,
                    "longitude": dc.coordinates.lng
                },
                "areaServed": {
                    "@type": "GeoCircle",
                    "geoMidpoint": {
                        "@type": "GeoCoordinates",
                        "latitude": dc.coordinates.lat,
                        "longitude": dc.coordinates.lng
                    },
                    "geoRadius": "2000000"
                },
                "serviceArea": dc.region,
                "currenciesAccepted": dc.currency,
                "paymentAccepted": ["Credit Card", "Bank Transfer", "Cryptocurrency"],
                "priceRange": "€€€"
            };

            this.localSEOSignals.set(dc.code, localSignals);
        });
    }

    setupHreflangOptimization() {
        // Dynamic hreflang implementation based on available languages
        const currentPath = window.location.pathname;
        const baseUrl = 'https://x-zoneservers.com';
        
        // Language mappings for different regions
        const languageRegions = {
            'en': ['US', 'GB', 'AU', 'SG'],
            'de': ['DE', 'AT', 'CH'],
            'nl': ['NL', 'BE'],
            'fr': ['FR', 'BE', 'CH'],
            'es': ['ES', 'MX', 'AR'],
            'ja': ['JP'],
            'zh': ['CN', 'TW', 'HK']
        };

        Object.entries(languageRegions).forEach(([lang, regions]) => {
            regions.forEach(region => {
                const hreflang = document.createElement('link');
                hreflang.rel = 'alternate';
                hreflang.hreflang = `${lang}-${region}`;
                hreflang.href = `${baseUrl}/${lang}${currentPath}`;
                document.head.appendChild(hreflang);
            });
        });

        // Add x-default for international users
        const defaultHreflang = document.createElement('link');
        defaultHreflang.rel = 'alternate';
        defaultHreflang.hreflang = 'x-default';
        defaultHreflang.href = `${baseUrl}${currentPath}`;
        document.head.appendChild(defaultHreflang);
    }

    setupLocalSearchOptimization() {
        // Optimize for local search queries
        const localKeywords = this.generateLocalKeywords();
        
        // Add local search meta tags
        const localMeta = document.createElement('meta');
        localMeta.name = 'local-keywords';
        localMeta.content = localKeywords.join(', ');
        document.head.appendChild(localMeta);

        // Monitor local search performance
        this.trackLocalSearchMetrics();
    }

    generateLocalKeywords() {
        const keywords = [];
        
        this.datacenters.forEach(dc => {
            keywords.push(
                `${dc.name} hosting`,
                `${dc.name} datacenter`,
                `${dc.name} servers`,
                `${dc.country} hosting`,
                `${dc.region} hosting`,
                `hosting in ${dc.name}`,
                `servers in ${dc.name}`,
                `${dc.name} VPS`,
                `${dc.name} dedicated servers`
            );
        });

        return [...new Set(keywords)]; // Remove duplicates
    }

    trackLocalSearchMetrics() {
        // Track local search performance metrics
        if (typeof gtag !== 'undefined') {
            const nearestDC = this.geoTargeting.get('userLocation')?.recommendedDatacenter;
            
            gtag('event', 'geographic_optimization', {
                'nearest_datacenter': nearestDC,
                'geo_targeting_active': true,
                'local_seo_optimized': true
            });
        }
    }

    // Get all geographic data for debugging
    getGeographicData() {
        return {
            datacenters: Object.fromEntries(this.datacenters),
            geoTargeting: Object.fromEntries(this.geoTargeting),
            localSEOSignals: Object.fromEntries(this.localSEOSignals)
        };
    }

    // Add missing method to prevent errors
    addGeographicStructuredData() {
        // Add geographic structured data to the page
        const structuredData = {
            "@context": "https://schema.org",
            "@type": "Organization",
            "name": "X-ZoneServers",
            "address": {
                "@type": "PostalAddress",
                "addressCountry": "NL"
            },
            "areaServed": ["Europe", "North America"],
            "serviceArea": {
                "@type": "GeoCircle",
                "geoMidpoint": {
                    "@type": "GeoCoordinates",
                    "latitude": 52.3676,
                    "longitude": 4.9041
                },
                "geoRadius": "5000000"
            }
        };

        const script = document.createElement('script');
        script.type = 'application/ld+json';
        script.textContent = JSON.stringify(structuredData);
        document.head.appendChild(script);
    }
}

// Initialize Geographic SEO Optimizer
if (typeof window !== 'undefined') {
    window.geoSEO = new GeographicSEOOptimizer();
    
    // Expose for debugging
    window.getGeographicData = () => {
        return window.geoSEO.getGeographicData();
    };
}
