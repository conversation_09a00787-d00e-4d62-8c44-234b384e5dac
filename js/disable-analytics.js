/**
 * Disable Analytics for Testing
 * This script disables all analytics and beacon API calls for testing purposes
 */

// Override navigator.sendBeacon to prevent beacon API calls
if (typeof navigator !== 'undefined' && navigator.sendBeacon) {
    const originalSendBeacon = navigator.sendBeacon.bind(navigator);
    
    navigator.sendBeacon = function(url, data) {
        console.log('🚫 Beacon API call blocked for testing:', url);
        return true; // Return true to indicate "success" without actually sending
    };
    
    console.log('📊 Analytics disabled for testing environment');
}

// Disable console errors for missing analytics endpoints
if (!window.originalFetchBackup) {
    window.originalFetchBackup = window.fetch.bind(window);
}
// Patch fetch only once to avoid duplicate variable issues
if (!window.__fetchPatchedForTesting) {
    window.__fetchPatchedForTesting = true;
    window.fetch = function(url, options) {
        if (typeof url === 'string' && (url.includes('/analytics/') || url.includes('/api/analytics/'))) {
            console.log('🚫 Analytics fetch blocked for testing:', url);
            return Promise.resolve(new Response('{"status":"disabled"}', {
                status: 200,
                statusText: 'OK',
                headers: { 'Content-Type': 'application/json' }
            }));
        }
        return window.originalFetchBackup.apply(this, arguments);
    };
}

// Prevent 404.html redirect loops
const originalBeaconAPI = navigator.sendBeacon;
if (originalBeaconAPI) {
    navigator.sendBeacon = function(url, data) {
        if (url && (url.includes('404.html') || url.includes('test.x-zoneservers.com'))) {
            console.log('🚫 Blocked problematic beacon call:', url);
            return true;
        }
        return originalBeaconAPI.call(this, url, data);
    };
}

// Disable service worker for testing to prevent caching conflicts
if ('serviceWorker' in navigator) {
    navigator.serviceWorker.getRegistrations().then(function(registrations) {
        for(let registration of registrations) {
            registration.unregister().then(function(boolean) {
                console.log('🔧 Service worker unregistered for testing');
            });
        }
    });
}

// IMPORTANT: Do NOT force reload scripts in testing — it causes duplicate declarations
console.log('🧪 Script auto-reload disabled in testing to prevent duplicate JS execution');

console.log('✅ Analytics and beacon API calls disabled for testing');
