<?php
/**
 * Knowledge Base Admin Interface for WHMCS
 * Complete management system for knowledge base articles and categories
 */

require_once 'whmcs-kb-module.php';

// Initialize KB module
$kbModule = init_whmcs_kb_module();

// Handle form submissions
$message = '';
$messageType = '';

if ($_POST) {
    switch ($_POST['action']) {
        case 'install':
            $result = $kbModule->installModule();
            $message = $result['message'];
            $messageType = $result['success'] ? 'success' : 'error';
            break;
            
        case 'create_article':
            $articleData = [
                'title' => $_POST['title'],
                'slug' => $_POST['slug'] ?? '',
                'content' => $_POST['content'],
                'excerpt' => $_POST['excerpt'] ?? '',
                'category_id' => intval($_POST['category_id']),
                'meta_title' => $_POST['meta_title'] ?? '',
                'meta_description' => $_POST['meta_description'] ?? '',
                'keywords' => $_POST['keywords'] ?? '',
                'tags' => isset($_POST['tags']) ? array_filter(explode(',', $_POST['tags'])) : [],
                'author_name' => $_POST['author_name'],
                'author_email' => $_POST['author_email'],
                'difficulty' => $_POST['difficulty'] ?? 'beginner',
                'status' => $_POST['status'] ?? 'draft',
                'is_featured' => isset($_POST['is_featured']) ? true : false,
                'published_at' => $_POST['status'] === 'published' ? date('Y-m-d H:i:s') : null
            ];
            
            $result = $kbModule->createArticle($articleData);
            $message = $result['message'];
            $messageType = $result['success'] ? 'success' : 'error';
            break;
            
        case 'create_category':
            try {
                $categoryData = [
                    'name' => $_POST['category_name'],
                    'slug' => $_POST['category_slug'] ?? strtolower(str_replace(' ', '-', $_POST['category_name'])),
                    'description' => $_POST['category_description'] ?? '',
                    'icon' => $_POST['category_icon'] ?? 'folder',
                    'color' => $_POST['category_color'] ?? '#3B82F6',
                    'sort_order' => intval($_POST['sort_order'] ?? 0),
                    'is_featured' => isset($_POST['category_featured']) ? 1 : 0,
                    'status' => 'active',
                    'created_at' => date('Y-m-d H:i:s'),
                    'updated_at' => date('Y-m-d H:i:s')
                ];
                
                // Insert category using Capsule
                $categoryId = \WHMCS\Database\Capsule::table('mod_kb_categories')->insertGetId($categoryData);
                
                $message = 'Category created successfully';
                $messageType = 'success';
            } catch (Exception $e) {
                $message = 'Failed to create category: ' . $e->getMessage();
                $messageType = 'error';
            }
            break;
    }
}

// Get existing articles and categories
$articlesResult = $kbModule->getArticles(['limit' => 50]);
$articles = $articlesResult['success'] ? $articlesResult['articles'] : [];

$categoriesResult = $kbModule->getCategories();
$categories = $categoriesResult['success'] ? $categoriesResult['categories'] : [];
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Knowledge Base Admin - X-ZoneServers</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script src="https://unpkg.com/lucide@latest" defer></script>
    <style>
        .message-success { @apply bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded mb-4; }
        .message-error { @apply bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-4; }
        .tab-content { display: none; }
        .tab-content.active { display: block; }
        .difficulty-badge {
            @apply px-2 py-1 rounded-full text-xs font-medium;
        }
        .difficulty-beginner { @apply bg-green-100 text-green-800; }
        .difficulty-intermediate { @apply bg-yellow-100 text-yellow-800; }
        .difficulty-advanced { @apply bg-red-100 text-red-800; }
    </style>
</head>
<body class="bg-gray-100 min-h-screen">
    <div class="container mx-auto px-4 py-8">
        <div class="bg-white rounded-lg shadow-lg">
            <!-- Header -->
            <div class="bg-indigo-600 text-white p-6 rounded-t-lg">
                <h1 class="text-2xl font-bold flex items-center">
                    <svg class="w-8 h-8 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                    </svg>
                    X-ZoneServers Knowledge Base Admin
                </h1>
                <p class="text-indigo-100 mt-2">Manage support articles and documentation</p>
            </div>

            <!-- Messages -->
            <?php if ($message): ?>
                <div class="p-6 pb-0">
                    <div class="message-<?php echo $messageType; ?>">
                        <?php echo htmlspecialchars($message); ?>
                    </div>
                </div>
            <?php endif; ?>

            <!-- Navigation Tabs -->
            <div class="p-6">
                <div class="border-b border-gray-200 mb-6">
                    <nav class="-mb-px flex space-x-8">
                        <button onclick="showTab('articles')" id="tab-articles" class="tab-button border-indigo-500 text-indigo-600 border-b-2 py-2 px-1 text-sm font-medium">
                            Articles
                        </button>
                        <button onclick="showTab('create-article')" id="tab-create-article" class="tab-button border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300 border-b-2 py-2 px-1 text-sm font-medium">
                            Create Article
                        </button>
                        <button onclick="showTab('categories')" id="tab-categories" class="tab-button border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300 border-b-2 py-2 px-1 text-sm font-medium">
                            Categories
                        </button>
                        <button onclick="showTab('analytics')" id="tab-analytics" class="tab-button border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300 border-b-2 py-2 px-1 text-sm font-medium">
                            Analytics
                        </button>
                        <button onclick="showTab('setup')" id="tab-setup" class="tab-button border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300 border-b-2 py-2 px-1 text-sm font-medium">
                            Setup
                        </button>
                    </nav>
                </div>

                <!-- Articles Tab -->
                <div id="content-articles" class="tab-content active">
                    <div class="flex justify-between items-center mb-6">
                        <h2 class="text-xl font-semibold text-gray-900">Knowledge Base Articles</h2>
                        <div class="flex gap-3">
                            <input type="text" placeholder="Search articles..." class="px-4 py-2 border border-gray-300 rounded-lg" id="article-search">
                            <select class="px-4 py-2 border border-gray-300 rounded-lg" id="article-filter">
                                <option value="">All Categories</option>
                                <?php foreach ($categories as $category): ?>
                                <option value="<?php echo $category->id; ?>"><?php echo htmlspecialchars($category->name); ?></option>
                                <?php endforeach; ?>
                            </select>
                        </div>
                    </div>
                    
                    <?php if (empty($articles)): ?>
                        <div class="text-center py-12">
                            <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                            </svg>
                            <h3 class="mt-2 text-sm font-medium text-gray-900">No articles found</h3>
                            <p class="mt-1 text-sm text-gray-500">Get started by creating your first knowledge base article.</p>
                            <button onclick="showTab('create-article')" class="mt-4 bg-indigo-600 text-white px-4 py-2 rounded-md hover:bg-indigo-700">
                                Create Article
                            </button>
                        </div>
                    <?php else: ?>
                        <div class="grid gap-4">
                            <?php foreach ($articles as $article): ?>
                                <div class="bg-gray-50 rounded-lg p-6 hover:bg-gray-100 transition-colors">
                                    <div class="flex justify-between items-start">
                                        <div class="flex-1">
                                            <div class="flex items-center gap-3 mb-2">
                                                <h3 class="text-lg font-semibold text-gray-900">
                                                    <?php echo htmlspecialchars($article->title); ?>
                                                </h3>
                                                <?php if ($article->is_featured): ?>
                                                <span class="bg-yellow-100 text-yellow-800 px-2 py-1 rounded-full text-xs font-medium">Featured</span>
                                                <?php endif; ?>
                                                <span class="difficulty-badge difficulty-<?php echo $article->difficulty; ?>">
                                                    <?php echo ucfirst($article->difficulty); ?>
                                                </span>
                                            </div>
                                            
                                            <p class="text-gray-600 mb-3">
                                                <?php echo htmlspecialchars($article->excerpt); ?>
                                            </p>
                                            
                                            <div class="flex flex-wrap items-center gap-4 text-sm text-gray-500">
                                                <span class="flex items-center">
                                                    <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 7h.01M7 3h5c.512 0 1.024.195 1.414.586l7 7a2 2 0 010 2.828l-7 7a2 2 0 01-2.828 0l-7-7A1.994 1.994 0 013 12V7a4 4 0 014-4z"></path>
                                                    </svg>
                                                    <?php echo htmlspecialchars($article->category_name); ?>
                                                </span>
                                                <span class="flex items-center">
                                                    <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"></path>
                                                    </svg>
                                                    <?php echo htmlspecialchars($article->author_name); ?>
                                                </span>
                                                <span class="flex items-center">
                                                    <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
                                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"></path>
                                                    </svg>
                                                    <?php echo number_format($article->view_count); ?> views
                                                </span>
                                                <span class="flex items-center">
                                                    <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M14 10h4.764a2 2 0 011.789 2.894l-3.5 7A2 2 0 0115.263 21h-4.017c-.163 0-.326-.02-.485-.06L7 20m7-10V5a2 2 0 00-2-2h-.095c-.5 0-.905.405-.905.905 0 .714-.211 1.412-.608 2.006L9 7m5 3v3.5M9 7l-3.325 1.663a.5.5 0 01-.65-.62L7 3"></path>
                                                    </svg>
                                                    <?php echo $article->helpful_votes; ?>/<?php echo ($article->helpful_votes + $article->not_helpful_votes); ?> helpful
                                                </span>
                                                <span class="flex items-center">
                                                    <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                                    </svg>
                                                    <?php echo $article->estimated_read_time; ?> min read
                                                </span>
                                            </div>
                                        </div>
                                        
                                        <div class="ml-4 flex flex-col items-end">
                                            <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium 
                                                <?php echo $article->status === 'published' ? 'bg-green-100 text-green-800' : ($article->status === 'draft' ? 'bg-yellow-100 text-yellow-800' : 'bg-gray-100 text-gray-800'); ?>">
                                                <?php echo ucfirst($article->status); ?>
                                            </span>
                                            <div class="mt-2 flex gap-2">
                                                <a href="/kb/<?php echo htmlspecialchars($article->slug); ?>/" target="_blank" 
                                                   class="text-indigo-600 hover:text-indigo-800 text-sm">View</a>
                                                <a href="#" class="text-gray-600 hover:text-gray-800 text-sm">Edit</a>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            <?php endforeach; ?>
                        </div>
                    <?php endif; ?>
                </div>

                <!-- Create Article Tab -->
                <div id="content-create-article" class="tab-content">
                    <h2 class="text-xl font-semibold text-gray-900 mb-6">Create Knowledge Base Article</h2>
                    
                    <form method="POST" class="space-y-6">
                        <input type="hidden" name="action" value="create_article">
                        
                        <!-- Basic Information -->
                        <div class="bg-gray-50 p-6 rounded-lg">
                            <h3 class="text-lg font-medium text-gray-900 mb-4">Basic Information</h3>
                            
                            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                                <div>
                                    <label for="title" class="block text-sm font-medium text-gray-700 mb-2">Article Title *</label>
                                    <input type="text" name="title" id="title" required
                                           class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500"
                                           placeholder="How to configure your server">
                                </div>
                                
                                <div>
                                    <label for="slug" class="block text-sm font-medium text-gray-700 mb-2">URL Slug</label>
                                    <input type="text" name="slug" id="slug"
                                           class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500"
                                           placeholder="how-to-configure-your-server">
                                    <p class="text-xs text-gray-500 mt-1">Leave blank to auto-generate from title</p>
                                </div>
                            </div>
                            
                            <div class="mt-6 grid grid-cols-1 md:grid-cols-2 gap-6">
                                <div>
                                    <label for="category_id" class="block text-sm font-medium text-gray-700 mb-2">Category *</label>
                                    <select name="category_id" id="category_id" required
                                            class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500">
                                        <option value="">Select Category</option>
                                        <?php foreach ($categories as $category): ?>
                                        <option value="<?php echo $category->id; ?>"><?php echo htmlspecialchars($category->name); ?></option>
                                        <?php endforeach; ?>
                                    </select>
                                </div>
                                
                                <div>
                                    <label for="difficulty" class="block text-sm font-medium text-gray-700 mb-2">Difficulty Level</label>
                                    <select name="difficulty" id="difficulty"
                                            class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500">
                                        <option value="beginner">Beginner</option>
                                        <option value="intermediate">Intermediate</option>
                                        <option value="advanced">Advanced</option>
                                    </select>
                                </div>
                            </div>
                            
                            <div class="mt-6">
                                <label for="excerpt" class="block text-sm font-medium text-gray-700 mb-2">Article Summary</label>
                                <textarea name="excerpt" id="excerpt" rows="3"
                                          class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500"
                                          placeholder="Brief description of what this article covers"></textarea>
                                <p class="text-xs text-gray-500 mt-1">Auto-generated from content if left empty</p>
                            </div>
                        </div>

                        <!-- Content -->
                        <div class="bg-gray-50 p-6 rounded-lg">
                            <h3 class="text-lg font-medium text-gray-900 mb-4">Article Content</h3>
                            
                            <div>
                                <label for="content" class="block text-sm font-medium text-gray-700 mb-2">Content *</label>
                                <textarea name="content" id="content" rows="20" required
                                          class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500"
                                          placeholder="Enter your article content here. You can use HTML for formatting."></textarea>
                                <p class="text-xs text-gray-500 mt-1">HTML formatting supported</p>
                            </div>
                        </div>

                        <!-- SEO Settings -->
                        <div class="bg-gray-50 p-6 rounded-lg">
                            <h3 class="text-lg font-medium text-gray-900 mb-4">SEO Settings</h3>
                            
                            <div class="space-y-4">
                                <div>
                                    <label for="meta_title" class="block text-sm font-medium text-gray-700 mb-2">Meta Title</label>
                                    <input type="text" name="meta_title" id="meta_title" maxlength="60"
                                           class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500"
                                           placeholder="SEO title (defaults to article title)">
                                    <p class="text-xs text-gray-500 mt-1">Recommended: 50-60 characters</p>
                                </div>
                                
                                <div>
                                    <label for="meta_description" class="block text-sm font-medium text-gray-700 mb-2">Meta Description</label>
                                    <textarea name="meta_description" id="meta_description" rows="2" maxlength="160"
                                              class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500"
                                              placeholder="Brief description for search engines"></textarea>
                                    <p class="text-xs text-gray-500 mt-1">Recommended: 150-160 characters</p>
                                </div>
                                
                                <div>
                                    <label for="keywords" class="block text-sm font-medium text-gray-700 mb-2">Keywords</label>
                                    <input type="text" name="keywords" id="keywords"
                                           class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500"
                                           placeholder="keyword1, keyword2, keyword3">
                                </div>
                                
                                <div>
                                    <label for="tags" class="block text-sm font-medium text-gray-700 mb-2">Tags</label>
                                    <input type="text" name="tags" id="tags"
                                           class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500"
                                           placeholder="server, configuration, linux">
                                    <p class="text-xs text-gray-500 mt-1">Comma-separated list</p>
                                </div>
                            </div>
                        </div>

                        <!-- Author & Publishing -->
                        <div class="bg-gray-50 p-6 rounded-lg">
                            <h3 class="text-lg font-medium text-gray-900 mb-4">Author & Publishing</h3>
                            
                            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                                <div>
                                    <label for="author_name" class="block text-sm font-medium text-gray-700 mb-2">Author Name *</label>
                                    <input type="text" name="author_name" id="author_name" required
                                           class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500"
                                           placeholder="John Smith">
                                </div>
                                
                                <div>
                                    <label for="author_email" class="block text-sm font-medium text-gray-700 mb-2">Author Email *</label>
                                    <input type="email" name="author_email" id="author_email" required
                                           class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500"
                                           placeholder="<EMAIL>">
                                </div>
                            </div>
                            
                            <div class="mt-6 flex items-center">
                                <input type="checkbox" name="is_featured" id="is_featured" 
                                       class="w-4 h-4 text-indigo-600 border-gray-300 rounded focus:ring-indigo-500">
                                <label for="is_featured" class="ml-2 text-sm text-gray-700">Mark as Featured Article</label>
                            </div>
                            
                            <div class="mt-6">
                                <label class="block text-sm font-medium text-gray-700 mb-2">Publishing Status</label>
                                <div class="space-y-2">
                                    <label class="flex items-center">
                                        <input type="radio" name="status" value="draft" checked
                                               class="w-4 h-4 text-indigo-600 border-gray-300 focus:ring-indigo-500">
                                        <span class="ml-2 text-sm text-gray-700">Save as Draft</span>
                                    </label>
                                    <label class="flex items-center">
                                        <input type="radio" name="status" value="published"
                                               class="w-4 h-4 text-indigo-600 border-gray-300 focus:ring-indigo-500">
                                        <span class="ml-2 text-sm text-gray-700">Publish Now</span>
                                    </label>
                                </div>
                            </div>
                        </div>

                        <div class="flex justify-end space-x-4">
                            <button type="button" onclick="showTab('articles')"
                                    class="px-6 py-2 border border-gray-300 text-gray-700 rounded-md hover:bg-gray-50">
                                Cancel
                            </button>
                            <button type="submit"
                                    class="px-6 py-2 bg-indigo-600 text-white rounded-md hover:bg-indigo-700">
                                Create Article
                            </button>
                        </div>
                    </form>
                </div>

                <!-- Categories Tab -->
                <div id="content-categories" class="tab-content">
                    <div class="flex justify-between items-center mb-6">
                        <h2 class="text-xl font-semibold text-gray-900">Knowledge Base Categories</h2>
                        <button onclick="showCreateCategoryForm()" class="bg-indigo-600 text-white px-4 py-2 rounded-md hover:bg-indigo-700">
                            Add Category
                        </button>
                    </div>
                    
                    <!-- Create Category Form (Initially Hidden) -->
                    <div id="create-category-form" class="bg-gray-50 p-6 rounded-lg mb-6 hidden">
                        <h3 class="text-lg font-medium text-gray-900 mb-4">Create New Category</h3>
                        <form method="POST" class="grid grid-cols-1 md:grid-cols-2 gap-4">
                            <input type="hidden" name="action" value="create_category">
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2">Category Name *</label>
                                <input type="text" name="category_name" required
                                       class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-indigo-500">
                            </div>
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2">URL Slug</label>
                                <input type="text" name="category_slug"
                                       class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-indigo-500">
                            </div>
                            <div class="md:col-span-2">
                                <label class="block text-sm font-medium text-gray-700 mb-2">Description</label>
                                <textarea name="category_description" rows="2"
                                          class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-indigo-500"></textarea>
                            </div>
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2">Icon</label>
                                <input type="text" name="category_icon" placeholder="folder"
                                       class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-indigo-500">
                            </div>
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2">Color</label>
                                <input type="color" name="category_color" value="#3B82F6"
                                       class="w-full h-10 border border-gray-300 rounded-md">
                            </div>
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2">Sort Order</label>
                                <input type="number" name="sort_order" value="0"
                                       class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-indigo-500">
                            </div>
                            <div class="flex items-center">
                                <input type="checkbox" name="category_featured" class="w-4 h-4 text-indigo-600 border-gray-300 rounded">
                                <label class="ml-2 text-sm text-gray-700">Featured Category</label>
                            </div>
                            <div class="md:col-span-2 flex justify-end space-x-4">
                                <button type="button" onclick="hideCreateCategoryForm()" class="px-4 py-2 border border-gray-300 text-gray-700 rounded-md hover:bg-gray-50">
                                    Cancel
                                </button>
                                <button type="submit" class="px-4 py-2 bg-indigo-600 text-white rounded-md hover:bg-indigo-700">
                                    Create Category
                                </button>
                            </div>
                        </form>
                    </div>
                    
                    <!-- Categories List -->
                    <div class="grid gap-4">
                        <?php foreach ($categories as $category): ?>
                            <div class="bg-white border border-gray-200 rounded-lg p-6">
                                <div class="flex items-center justify-between">
                                    <div class="flex items-center">
                                        <div class="w-12 h-12 rounded-lg flex items-center justify-center text-white" 
                                             style="background-color: <?php echo $category->color; ?>">
                                            <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" 
                                                      d="M3 7v10a2 2 0 002 2h14a2 2 0 002-2V9a2 2 0 00-2-2H5a2 2 0 00-2-2z"></path>
                                            </svg>
                                        </div>
                                        <div class="ml-4">
                                            <h3 class="text-lg font-semibold text-gray-900 flex items-center">
                                                <?php echo htmlspecialchars($category->name); ?>
                                                <?php if ($category->is_featured): ?>
                                                <span class="ml-2 bg-yellow-100 text-yellow-800 px-2 py-1 rounded-full text-xs font-medium">Featured</span>
                                                <?php endif; ?>
                                            </h3>
                                            <p class="text-gray-600"><?php echo htmlspecialchars($category->description); ?></p>
                                            <div class="text-sm text-gray-500 mt-1">
                                                <?php echo $category->article_count; ?> articles • Sort order: <?php echo $category->sort_order; ?>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="flex items-center space-x-2">
                                        <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium 
                                            <?php echo $category->status === 'active' ? 'bg-green-100 text-green-800' : 'bg-gray-100 text-gray-800'; ?>">
                                            <?php echo ucfirst($category->status); ?>
                                        </span>
                                        <button class="text-indigo-600 hover:text-indigo-800 text-sm">Edit</button>
                                    </div>
                                </div>
                            </div>
                        <?php endforeach; ?>
                    </div>
                </div>

                <!-- Analytics Tab -->
                <div id="content-analytics" class="tab-content">
                    <h2 class="text-xl font-semibold text-gray-900 mb-6">Knowledge Base Analytics</h2>
                    
                    <div class="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
                        <!-- Stats Cards -->
                        <div class="bg-white border border-gray-200 rounded-lg p-6">
                            <div class="flex items-center">
                                <div class="flex-shrink-0">
                                    <svg class="h-8 w-8 text-indigo-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                                    </svg>
                                </div>
                                <div class="ml-5 w-0 flex-1">
                                    <dl>
                                        <dt class="text-sm font-medium text-gray-500 truncate">Total Articles</dt>
                                        <dd class="text-lg font-medium text-gray-900"><?php echo count($articles); ?></dd>
                                    </dl>
                                </div>
                            </div>
                        </div>
                        
                        <div class="bg-white border border-gray-200 rounded-lg p-6">
                            <div class="flex items-center">
                                <div class="flex-shrink-0">
                                    <svg class="h-8 w-8 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"></path>
                                    </svg>
                                </div>
                                <div class="ml-5 w-0 flex-1">
                                    <dl>
                                        <dt class="text-sm font-medium text-gray-500 truncate">Total Views</dt>
                                        <dd class="text-lg font-medium text-gray-900">
                                            <?php echo number_format(array_sum(array_column($articles->toArray() ?? [], 'view_count'))); ?>
                                        </dd>
                                    </dl>
                                </div>
                            </div>
                        </div>
                        
                        <div class="bg-white border border-gray-200 rounded-lg p-6">
                            <div class="flex items-center">
                                <div class="flex-shrink-0">
                                    <svg class="h-8 w-8 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 7h.01M7 3h5c.512 0 1.024.195 1.414.586l7 7a2 2 0 010 2.828l-7 7a2 2 0 01-2.828 0l-7-7A1.994 1.994 0 013 12V7a4 4 0 014-4z"></path>
                                    </svg>
                                </div>
                                <div class="ml-5 w-0 flex-1">
                                    <dl>
                                        <dt class="text-sm font-medium text-gray-500 truncate">Categories</dt>
                                        <dd class="text-lg font-medium text-gray-900"><?php echo count($categories); ?></dd>
                                    </dl>
                                </div>
                            </div>
                        </div>
                        
                        <div class="bg-white border border-gray-200 rounded-lg p-6">
                            <div class="flex items-center">
                                <div class="flex-shrink-0">
                                    <svg class="h-8 w-8 text-yellow-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11.049 2.927c.3-.921 1.603-.921 1.902 0l1.519 4.674a1 1 0 00.95.69h4.915c.969 0 1.371 1.24.588 1.81l-3.976 2.888a1 1 0 00-.363 1.118l1.518 4.674c.3.922-.755 1.688-1.538 1.118l-3.976-2.888a1 1 0 00-1.176 0l-3.976 2.888c-.783.57-1.838-.197-1.538-1.118l1.518-4.674a1 1 0 00-.363-1.118l-3.976-2.888c-.784-.57-.38-1.81.588-1.81h4.914a1 1 0 00.951-.69l1.519-4.674z"></path>
                                    </svg>
                                </div>
                                <div class="ml-5 w-0 flex-1">
                                    <dl>
                                        <dt class="text-sm font-medium text-gray-500 truncate">Featured Articles</dt>
                                        <dd class="text-lg font-medium text-gray-900">
                                            <?php echo count(array_filter($articles->toArray() ?? [], function($article) { return $article['is_featured']; })); ?>
                                        </dd>
                                    </dl>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="bg-white border border-gray-200 rounded-lg p-6">
                        <h3 class="text-lg font-medium text-gray-900 mb-4">Most Popular Articles</h3>
                        <div class="space-y-3">
                            <?php 
                            $popularArticles = collect($articles)->sortByDesc('view_count')->take(5);
                            foreach ($popularArticles as $article): 
                            ?>
                                <div class="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                                    <div>
                                        <h4 class="font-medium text-gray-900"><?php echo htmlspecialchars($article->title); ?></h4>
                                        <p class="text-sm text-gray-500"><?php echo htmlspecialchars($article->category_name); ?></p>
                                    </div>
                                    <div class="text-sm text-gray-600">
                                        <?php echo number_format($article->view_count); ?> views
                                    </div>
                                </div>
                            <?php endforeach; ?>
                        </div>
                    </div>
                </div>

                <!-- Setup Tab -->
                <div id="content-setup" class="tab-content">
                    <h2 class="text-xl font-semibold text-gray-900 mb-6">Knowledge Base Setup</h2>
                    
                    <div class="bg-yellow-50 border border-yellow-200 rounded-lg p-6 mb-6">
                        <div class="flex items-start">
                            <svg class="w-5 h-5 text-yellow-400 mt-0.5 mr-3" fill="currentColor" viewBox="0 0 20 20">
                                <path fill-rule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clip-rule="evenodd"></path>
                            </svg>
                            <div>
                                <h3 class="text-sm font-medium text-yellow-800">Setup Required</h3>
                                <p class="mt-1 text-sm text-yellow-700">
                                    Before using the knowledge base system, you need to install the database tables and configure WHMCS integration.
                                </p>
                            </div>
                        </div>
                    </div>
                    
                    <div class="space-y-6">
                        <div class="bg-white border border-gray-200 rounded-lg p-6">
                            <h3 class="text-lg font-medium text-gray-900 mb-4">Install Knowledge Base Module</h3>
                            <p class="text-gray-600 mb-4">
                                This will create the necessary database tables in your WHMCS database to store knowledge base articles, categories, and analytics.
                            </p>
                            
                            <form method="POST" class="inline">
                                <input type="hidden" name="action" value="install">
                                <button type="submit" class="bg-green-600 text-white px-4 py-2 rounded-md hover:bg-green-700">
                                    Install Database Tables
                                </button>
                            </form>
                        </div>
                        
                        <div class="bg-white border border-gray-200 rounded-lg p-6">
                            <h3 class="text-lg font-medium text-gray-900 mb-4">Features Overview</h3>
                            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                                <div>
                                    <h4 class="font-medium text-gray-900 mb-2">Article Management</h4>
                                    <ul class="text-sm text-gray-600 space-y-1">
                                        <li>• Rich text content with HTML support</li>
                                        <li>• SEO optimization with meta tags</li>
                                        <li>• Difficulty levels (Beginner/Intermediate/Advanced)</li>
                                        <li>• Draft and publish workflow</li>
                                        <li>• Featured article highlighting</li>
                                    </ul>
                                </div>
                                <div>
                                    <h4 class="font-medium text-gray-900 mb-2">User Experience</h4>
                                    <ul class="text-sm text-gray-600 space-y-1">
                                        <li>• Full-text search functionality</li>
                                        <li>• Category-based organization</li>
                                        <li>• Article rating system</li>
                                        <li>• View count tracking</li>
                                        <li>• Mobile-responsive design</li>
                                    </ul>
                                </div>
                                <div>
                                    <h4 class="font-medium text-gray-900 mb-2">SEO Features</h4>
                                    <ul class="text-sm text-gray-600 space-y-1">
                                        <li>• Clean URL structure (/kb/article-slug/)</li>
                                        <li>• Schema.org structured data</li>
                                        <li>• XML sitemap generation</li>
                                        <li>• Meta tag optimization</li>
                                        <li>• Breadcrumb navigation</li>
                                    </ul>
                                </div>
                                <div>
                                    <h4 class="font-medium text-gray-900 mb-2">Analytics & Insights</h4>
                                    <ul class="text-sm text-gray-600 space-y-1">
                                        <li>• Article view tracking</li>
                                        <li>• Search query analytics</li>
                                        <li>• Helpfulness voting</li>
                                        <li>• Popular content identification</li>
                                        <li>• Category performance metrics</li>
                                    </ul>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        function showTab(tabName) {
            // Hide all tab contents
            document.querySelectorAll('.tab-content').forEach(content => {
                content.classList.remove('active');
            });
            
            // Remove active classes from all tab buttons
            document.querySelectorAll('.tab-button').forEach(button => {
                button.classList.remove('border-indigo-500', 'text-indigo-600');
                button.classList.add('border-transparent', 'text-gray-500');
            });
            
            // Show selected tab content
            document.getElementById('content-' + tabName).classList.add('active');
            
            // Add active classes to selected tab button
            const activeButton = document.getElementById('tab-' + tabName);
            activeButton.classList.add('border-indigo-500', 'text-indigo-600');
            activeButton.classList.remove('border-transparent', 'text-gray-500');
        }
        
        function showCreateCategoryForm() {
            document.getElementById('create-category-form').classList.remove('hidden');
        }
        
        function hideCreateCategoryForm() {
            document.getElementById('create-category-form').classList.add('hidden');
        }
        
        // Auto-generate slug from title
        document.getElementById('title')?.addEventListener('input', function() {
            const title = this.value;
            const slug = title.toLowerCase()
                .replace(/[^a-z0-9\s-]/g, '')
                .replace(/\s+/g, '-')
                .replace(/-+/g, '-')
                .trim('-');
            document.getElementById('slug').value = slug;
        });
    </script>
</body>
</html>