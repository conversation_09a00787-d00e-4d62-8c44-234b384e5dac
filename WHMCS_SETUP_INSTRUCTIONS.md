# WHMCS Integration Setup Instructions

Your contact form has been successfully integrated with WHMCS ticket creation system. Follow these steps to complete the setup:

## Files Created/Modified

1. **whmcs-ticket-handler.php** - Main PHP script that processes contact form submissions and creates WHMCS tickets
2. **whmcs-config.php** - Configuration file with WHMCS settings
3. **contact.html** - Modified to send form data to WHMCS handler
4. **WHMCS_SETUP_INSTRUCTIONS.md** - This setup guide

## Setup Steps

### 1. Configure WHMCS Settings

Edit the `whmcs-config.php` file and update the following:

```php
// WHMCS Installation URL
'whmcs_url' => 'https://YOUR-WHMCS-DOMAIN.com/includes/api.php',

// WHMCS Admin Credentials
'whmcs_username' => 'your-admin-username',
'whmcs_password' => 'your-api-password',
```

### 2. Get Your WHMCS Department IDs

1. Log into your WHMCS Admin Area
2. Go to **Support > Support Departments**
3. Note down the department IDs and update them in `whmcs-config.php`:

```php
'department_mapping' => [
    'sales' => 1,           // Replace with your Sales Department ID
    'technical' => 2,       // Replace with your Technical Support Department ID
    'billing' => 3,         // Replace with your Billing Department ID
    'partnership' => 1,     // Partnership inquiries (usually sales)
    'other' => 2            // General inquiries (usually technical)
],
```

### 3. Set Up API Authentication (Recommended)

For better security, use API authentication instead of admin password:

1. In WHMCS Admin Area, go to **Setup > Staff Management > Administrator Roles**
2. Create a new role with API access permissions
3. Create a new admin user with this role
4. Generate an API authentication token
5. Use the token in `whmcs_password` instead of the actual password

### 4. Test the Integration

1. Ensure your server has PHP with cURL enabled
2. Set debug mode to `true` in `whmcs-config.php` for testing:
   ```php
   'debug_mode' => true,
   ```
3. Fill out and submit the contact form
4. Check if tickets are created in WHMCS
5. Check server error logs for any issues

### 5. Production Setup

Once testing is complete:

1. Set debug mode to `false`:
   ```php
   'debug_mode' => false,
   ```
2. Ensure proper file permissions on PHP files
3. Consider setting up SSL certificates if not already done
4. Review and adjust logging settings as needed

## How It Works

1. User fills out the contact form on your website
2. Form data is sent via JavaScript fetch to `whmcs-ticket-handler.php`
3. The PHP script validates the data and formats it for WHMCS
4. Script calls WHMCS API to create a support ticket
5. User receives confirmation with ticket number

## Form Field Mapping

- **First Name + Last Name** → Ticket opener name
- **Email** → Ticket email address
- **Phone** → Added to ticket message (optional)
- **Subject** → Determines department and ticket subject prefix
- **Message** → Main ticket content

## Troubleshooting

### Common Issues:

1. **"Unable to create support ticket"**
   - Check WHMCS URL, username, and password in config
   - Verify API access is enabled for the admin user
   - Check server error logs

2. **"Method not allowed" error**
   - Ensure the form is submitting via POST method
   - Check if mod_rewrite or server configuration is blocking requests

3. **Tickets not appearing in correct department**
   - Verify department IDs in config match your WHMCS setup
   - Check if departments are active in WHMCS

4. **PHP errors**
   - Ensure server has PHP 7.0+ with cURL extension
   - Check file permissions
   - Review server error logs

### Debug Mode

Enable debug mode in `whmcs-config.php` to see detailed error messages:

```php
'debug_mode' => true,
```

This will display API error messages in the browser console and log detailed information.

## Security Considerations

1. Use API authentication tokens instead of admin passwords
2. Implement rate limiting to prevent spam submissions
3. Keep WHMCS and PHP updated
4. Use HTTPS for all communications
5. Regularly monitor error logs
6. Consider implementing CAPTCHA for additional spam protection

## Support

If you encounter issues during setup:

1. Check WHMCS API documentation: https://developers.whmcs.com/api/
2. Review server error logs
3. Test API connection directly using WHMCS API tools
4. Ensure all required PHP extensions are installed

Your contact form will now automatically create support tickets in WHMCS when submitted!