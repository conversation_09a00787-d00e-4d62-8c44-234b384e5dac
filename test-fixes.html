<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Fixes - X-ZoneServers</title>
    
    <!-- Content Security Policy -->
    <meta http-equiv="Content-Security-Policy" content="default-src 'self'; script-src 'self' 'unsafe-inline' 'unsafe-eval' https://cdnjs.cloudflare.com; style-src 'self' 'unsafe-inline' https://cdnjs.cloudflare.com; font-src 'self' https://fonts.gstatic.com https://cdnjs.cloudflare.com; img-src 'self' data: https:; connect-src 'self' https://ipapi.co; frame-src 'none'; object-src 'none';"
    
    <!-- Load Tailwind CSS from reliable CDN -->
    <link rel="preload" href="https://cdnjs.cloudflare.com/ajax/libs/tailwindcss/3.4.17/tailwind.min.css" as="style" onload="this.onload=null;this.rel='stylesheet'">
    <noscript><link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/tailwindcss/3.4.17/tailwind.min.css"></noscript>

    <!-- Load Lucide icons from reliable CDN -->
    <link rel="preload" href="https://cdnjs.cloudflare.com/ajax/libs/lucide/0.263.1/umd/lucide.min.js" as="script">
    <script src="https://cdnjs.cloudflare.com/ajax/libs/lucide/0.263.1/umd/lucide.min.js" async onload="initializeLucideIcons()"></script>

    <!-- Disable analytics for testing -->
    <script src="js/disable-analytics.js"></script>
</head>
<body class="bg-slate-950 text-white">
    <div class="container mx-auto p-8">
        <h1 class="text-3xl font-bold mb-6">Testing Fixes</h1>
        
        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div class="bg-slate-800 p-6 rounded-lg">
                <h2 class="text-xl font-semibold mb-4 flex items-center">
                    <i data-lucide="check-circle" class="w-5 h-5 mr-2 text-green-400"></i>
                    Tailwind CSS Test
                </h2>
                <p class="text-gray-300">If you can see this styled properly, Tailwind CSS is working.</p>
            </div>
            
            <div class="bg-slate-800 p-6 rounded-lg">
                <h2 class="text-xl font-semibold mb-4 flex items-center">
                    <i data-lucide="zap" class="w-5 h-5 mr-2 text-blue-400"></i>
                    Lucide Icons Test
                </h2>
                <p class="text-gray-300">If you can see icons, Lucide is working.</p>
            </div>
        </div>
        
        <div class="mt-8 bg-slate-800 p-6 rounded-lg">
            <h2 class="text-xl font-semibold mb-4">JavaScript Test</h2>
            <button id="test-btn" class="bg-blue-500 hover:bg-blue-600 px-4 py-2 rounded">
                Test JavaScript
            </button>
            <div id="test-result" class="mt-4 text-green-400"></div>
        </div>
    </div>

    <script>
        // Initialize Lucide icons when library is loaded
        function initializeLucideIcons() {
            if (typeof lucide !== 'undefined' && lucide.createIcons) {
                lucide.createIcons();
                console.log('✅ Lucide icons initialized successfully');
            } else {
                console.error('❌ Lucide library not available');
            }
        }

        // Test JavaScript functionality
        document.addEventListener('DOMContentLoaded', function() {
            const testBtn = document.getElementById('test-btn');
            const testResult = document.getElementById('test-result');
            
            if (testBtn && testResult) {
                testBtn.addEventListener('click', function() {
                    testResult.textContent = '✅ JavaScript is working correctly!';
                    console.log('✅ JavaScript test passed');
                });
            }
            
            // Test requestIdleCallback fallback
            if (typeof requestIdleCallback !== 'undefined') {
                console.log('✅ requestIdleCallback is available');
            } else {
                console.log('⚠️ requestIdleCallback not available, using setTimeout fallback');
            }
        });
    </script>
</body>
</html>
