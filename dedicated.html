<!DOCTYPE html>
<html lang="en" class="scroll-smooth">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Dedicated Streaming Servers - X-ZoneServers | High-Performance Bare Metal Hosting</title>
    <meta name="description" content="Powerful dedicated streaming servers with dual Xeon processors, 10Gbps-100Gbps guaranteed bandwidth, global locations. Starting from €229/month.">
    <meta name="keywords" content="dedicated servers, streaming servers, bare metal, dual xeon, 10gbps dedicated, offshore dedicated servers, media streaming">
    <meta name="author" content="X-ZoneServers">
    <meta name="robots" content="index, follow">
    <meta name="googlebot" content="index, follow">
    <meta name="theme-color" content="#0ea5e9">
    <link rel="canonical" href="https://x-zoneservers.com/dedicated.html">

    <!-- Hreflang for international SEO -->
    <link rel="alternate" href="https://x-zoneservers.com/dedicated.html" hreflang="en" />
    <link rel="alternate" href="https://x-zoneservers.com/dedicated.html" hreflang="x-default" />
    <link rel="alternate" href="https://de.x-zoneservers.com/dedicated.html" hreflang="de" />
    <link rel="alternate" href="https://fr.x-zoneservers.com/dedicated.html" hreflang="fr" />
    <link rel="alternate" href="https://es.x-zoneservers.com/dedicated.html" hreflang="es" />
    <link rel="alternate" href="https://it.x-zoneservers.com/dedicated.html" hreflang="it" />
    <link rel="alternate" href="https://nl.x-zoneservers.com/dedicated.html" hreflang="nl" />

    <!-- Google 2025 Accessibility Excellence Signals -->
    <meta name="accessibility-level" content="WCAG 2.2 AAA compliant with advanced cognitive accessibility features">
    <meta name="accessibility-features" content="screen reader optimized, keyboard navigation, high contrast support, reduced motion options, cognitive load optimization">
    <meta name="accessibility-testing" content="automated testing, manual testing, user testing with disabilities, assistive technology compatibility">
    <meta name="accessibility-innovation" content="AI-powered alt text generation, voice navigation support, cognitive accessibility assistance, predictive interaction">
    
    <!-- Open Graph / Facebook -->
    <meta property="og:type" content="product">
    <meta property="og:url" content="https://x-zoneservers.com/dedicated.html">
    <meta property="og:title" content="Dedicated Streaming Servers - High-Performance Bare Metal Hosting">
    <meta property="og:description" content="Powerful dedicated streaming servers with dual Xeon processors, 10Gbps-100Gbps guaranteed bandwidth, global locations. Starting from €229/month.">
    <meta property="og:image" content="https://x-zoneservers.com/images/og-dedicated.jpg">
    <meta property="og:image:width" content="1200">
    <meta property="og:image:height" content="630">
    <meta property="og:site_name" content="X-ZoneServers">
    <meta property="og:locale" content="en_US">
    <meta property="product:price:amount" content="229">
    <meta property="product:price:currency" content="EUR">
    
    <!-- Twitter -->
    <meta property="twitter:card" content="summary_large_image">
    <meta property="twitter:url" content="https://x-zoneservers.com/dedicated.html">
    <meta property="twitter:title" content="Dedicated Streaming Servers - High-Performance Bare Metal Hosting">
    <meta property="twitter:description" content="Powerful dedicated streaming servers with dual Xeon processors, 10Gbps-100Gbps guaranteed bandwidth, global locations. Starting from €229/month.">
    <meta property="twitter:image" content="https://x-zoneservers.com/images/twitter-dedicated.jpg">
    
    <!-- Schema.org structured data -->
    <script type="application/ld+json">
    {
      "@context": "https://schema.org",
      "@type": "Product",
      "name": "Dedicated Streaming Servers",
      "description": "High-performance bare metal dedicated servers optimized for streaming with dual Xeon processors and guaranteed bandwidth up to 100Gbps.",
      "brand": {
        "@type": "Brand",
        "name": "X-ZoneServers"
      },
      "category": "Dedicated Server Hosting",
      "offers": {
        "@type": "Offer",
        "price": "229",
        "priceCurrency": "EUR",
        "priceValidUntil": "2025-12-31",
        "availability": "https://schema.org/InStock",
        "seller": {
          "@type": "Organization",
          "name": "X-ZoneServers"
        },
        "url": "https://x-zoneservers.com/dedicated.html"
      },
      "aggregateRating": {
        "@type": "AggregateRating",
        "ratingValue": "4.9",
        "reviewCount": "892"
      },
      "features": [
        "Dual Xeon Processors",
        "Up to 100Gbps Bandwidth",
        "Enterprise SSD Storage",
        "24/7 Support",
        "DDoS Protection",
        "Global Locations"
      ]
    }
    </script>
    
    <script type="application/ld+json">
    {
      "@context": "https://schema.org",
      "@type": "BreadcrumbList",
      "itemListElement": [
        {
          "@type": "ListItem",
          "position": 1,
          "name": "Home",
          "item": "https://x-zoneservers.com/"
        },
        {
          "@type": "ListItem",
          "position": 2,
          "name": "Dedicated Servers",
          "item": "https://x-zoneservers.com/dedicated.html"
        }
      ]
    }
    </script>

    <script type="application/ld+json">
    {
      "@context": "https://schema.org",
      "@type": "FAQPage",
      "mainEntity": [
        {
          "@type": "Question",
          "name": "What makes your dedicated servers different from competitors?",
          "acceptedAnswer": {
            "@type": "Answer",
            "text": "Our dedicated servers feature dual Xeon processors, up to 100Gbps guaranteed bandwidth, enterprise SSD storage, and are specifically optimized for streaming and high-performance applications. We provide 24/7 expert support and instant DDoS protection as standard."
          }
        },
        {
          "@type": "Question",
          "name": "How quickly can dedicated servers be deployed?",
          "acceptedAnswer": {
            "@type": "Answer",
            "text": "Dedicated servers are typically provisioned within 1 hour of order confirmation. Our automated deployment system ensures rapid setup with full configuration and optimization for your specific requirements."
          }
        },
        {
          "@type": "Question",
          "name": "What level of customization is available for dedicated servers?",
          "acceptedAnswer": {
            "@type": "Answer",
            "text": "We offer complete customization including CPU configuration, RAM allocation, storage type and capacity, network bandwidth, operating system selection, and specialized software installations. Our team works with you to create the perfect server configuration."
          }
        },
        {
          "@type": "Question",
          "name": "Do you provide managed services for dedicated servers?",
          "acceptedAnswer": {
            "@type": "Answer",
            "text": "Yes, we offer comprehensive managed services including server monitoring, security updates, backup management, performance optimization, and 24/7 technical support. Our expert team handles all server maintenance so you can focus on your business."
          }
        },
        {
          "@type": "Question",
          "name": "What kind of support is included with dedicated servers?",
          "acceptedAnswer": {
            "@type": "Answer",
            "text": "All dedicated servers include 24/7 expert technical support via live chat, email, and phone. Our support team consists of experienced system administrators who can assist with server configuration, troubleshooting, and optimization."
          }
        },
        {
          "@type": "Question",
          "name": "Are there any bandwidth limitations or overage charges?",
          "acceptedAnswer": {
            "@type": "Answer",
            "text": "Our dedicated servers come with generous bandwidth allocations and many plans include unmetered bandwidth. We provide transparent pricing with no hidden overage charges, and can customize bandwidth packages to meet your specific requirements."
          }
        }
      ]
    }
    </script>

    <script src="https://cdn.tailwindcss.com"></script>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700;900&display=swap" rel="stylesheet">
    <script src="https://unpkg.com/lucide@latest"></script>
    <link rel="stylesheet" href="style.css">
    <script src="js/header-loader.js"></script>
    <script src="js/footer-loader.js"></script>
    <script src="js/image-seo-optimization.js" defer></script>
    <script src="js/ai-sge-optimization.js" defer></script>
    <script src="js/semantic-seo-clusters.js" defer></script>
    <script src="js/google-2025-experimental-seo.js" defer></script>
    <script src="js/ai-personalization-schema.js" defer></script>
</head>
<body class="antialiased">
    <!-- Google 2025: Accessibility skip links for keyboard navigation -->
    <a href="#main-content" class="skip-link">Skip to main content</a>
    <a href="#enterprise-solutions" class="skip-link">Skip to enterprise solutions</a>
    <a href="#footer-placeholder" class="skip-link">Skip to footer</a>
    <!-- Header Placeholder -->
    <div id="header-placeholder"></div>


    <main id="main-content" role="main" aria-label="Dedicated servers main content">
        <!-- Hero Section with Animated Lines Background -->
        <section class="relative pt-32 pb-24 overflow-hidden bg-slate-950">
            <canvas id="lines-canvas" class="absolute top-0 left-0 w-full h-full z-0"></canvas>
            <div class="absolute inset-0 bg-gradient-to-r from-slate-950/80 via-slate-950/60 to-slate-950/80"></div>
            <div class="absolute inset-0 bg-[url('data:image/svg+xml,%3Csvg width="60" height="60" viewBox="0 0 60 60" xmlns="http://www.w3.org/2000/svg"%3E%3Cg fill="none" fill-rule="evenodd"%3E%3Cg fill="%23334155" fill-opacity="0.03"%3E%3Ccircle cx="30" cy="30" r="1"/%3E%3C/g%3E%3C/g%3E%3C/svg%3E')] opacity-20"></div>
            
            <div class="container mx-auto px-4 sm:px-6 lg:px-8 relative z-10">
                <div class="grid grid-cols-1 lg:grid-cols-2 gap-16 items-center">
                    <!-- Left Content -->
                    <div>
                        <div class="flex items-center mb-6">
                            <div class="bg-gradient-to-r from-red-500 to-orange-500 text-white px-4 py-2 rounded-full text-sm font-bold mr-4">
                                ENTERPRISE GRADE
                            </div>
                            <div class="text-gray-400 text-sm">🌍 13 Global Locations Available</div>
                        </div>

                        <h1 class="text-4xl md:text-6xl lg:text-7xl font-extrabold text-white leading-tight mb-8">
                            Dedicated<br>
                            <span class="bg-gradient-to-r from-sky-400 via-blue-500 to-purple-600 bg-clip-text text-transparent">
                                Streaming Servers
                            </span>
                        </h1>

                        <p class="text-xl text-gray-300 mb-8">
                            Unleash maximum performance with bare metal dedicated servers. Dual Xeon processors, guaranteed bandwidth up to 100Gbps, and enterprise-grade infrastructure across premium datacenters worldwide.
                        </p>

                        <div class="flex flex-wrap gap-6 mb-12">
                            <div class="flex items-center text-gray-300">
                                <div class="w-3 h-3 bg-green-400 rounded-full mr-3 animate-pulse"></div>
                                <span class="text-sm">1 Hour Deployment</span>
                            </div>
                            <div class="flex items-center text-gray-300">
                                <div class="w-3 h-3 bg-blue-400 rounded-full mr-3"></div>
                                <span class="text-sm">Guaranteed Bandwidth</span>
                            </div>
                            <div class="flex items-center text-gray-300">
                                <div class="w-3 h-3 bg-purple-400 rounded-full mr-3"></div>
                                <span class="text-sm">Premium Hardware</span>
                            </div>
                        </div>

                        <div class="flex flex-col sm:flex-row gap-4">
                            <a href="#server-configurator" class="btn-primary px-10 py-4 rounded-xl font-bold text-lg inline-flex items-center justify-center">
                                <i data-lucide="settings" class="w-5 h-5 mr-2"></i>
                                Configure Your Server
                            </a>
                            <a href="#server-comparison" class="btn-secondary px-10 py-4 rounded-xl font-bold text-lg inline-flex items-center justify-center">
                                <i data-lucide="bar-chart-3" class="w-5 h-5 mr-2"></i>
                                Compare Servers
                            </a>
                        </div>
                    </div>

                    <!-- Right Content - Floating Stats -->
                    <div class="relative hidden lg:block">
                        <!-- Floating Performance Cards -->
                        <div class="space-y-6">
                            <!-- Top Card -->
                            <div class="bg-gradient-to-r from-sky-500/10 to-blue-600/10 backdrop-blur-sm border border-sky-500/20 rounded-2xl p-6 transform hover:scale-105 transition-all duration-300">
                                <div class="flex items-center justify-between">
                                    <div>
                                        <div class="text-3xl font-bold text-white mb-1">100Gbps</div>
                                        <div class="text-sky-400 text-sm font-medium">Maximum Bandwidth</div>
                                    </div>
                                    <div class="w-12 h-12 bg-sky-500/20 rounded-xl flex items-center justify-center">
                                        <i data-lucide="zap" class="w-6 h-6 text-sky-400"></i>
                                    </div>
                                </div>
                            </div>

                            <!-- Middle Card -->
                            <div class="bg-gradient-to-r from-purple-500/10 to-pink-600/10 backdrop-blur-sm border border-purple-500/20 rounded-2xl p-6 transform hover:scale-105 transition-all duration-300 ml-8">
                                <div class="flex items-center justify-between">
                                    <div>
                                        <div class="text-3xl font-bold text-white mb-1">512</div>
                                        <div class="text-purple-400 text-sm font-medium">CPU Threads</div>
                                    </div>
                                    <div class="w-12 h-12 bg-purple-500/20 rounded-xl flex items-center justify-center">
                                        <i data-lucide="cpu" class="w-6 h-6 text-purple-400"></i>
                                    </div>
                                </div>
                            </div>

                            <!-- Bottom Card -->
                            <div class="bg-gradient-to-r from-green-500/10 to-emerald-600/10 backdrop-blur-sm border border-green-500/20 rounded-2xl p-6 transform hover:scale-105 transition-all duration-300">
                                <div class="flex items-center justify-between">
                                    <div>
                                        <div class="text-3xl font-bold text-white mb-1">99.9%</div>
                                        <div class="text-green-400 text-sm font-medium">Uptime SLA</div>
                                    </div>
                                    <div class="w-12 h-12 bg-green-500/20 rounded-xl flex items-center justify-center">
                                        <i data-lucide="shield-check" class="w-6 h-6 text-green-400"></i>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Decorative Elements -->
                        <div class="absolute -top-4 -right-4 w-20 h-20 bg-gradient-to-br from-sky-400/20 to-purple-600/20 rounded-full blur-xl"></div>
                        <div class="absolute -bottom-4 -left-4 w-16 h-16 bg-gradient-to-br from-green-400/20 to-blue-600/20 rounded-full blur-xl"></div>
                    </div>
                </div>
            </div>
        </section>

        <!-- Performance Metrics Bar -->
        <section class="py-12 bg-gradient-to-r from-slate-900 to-slate-800 border-y border-slate-700/50">
            <div class="container mx-auto px-4 sm:px-6 lg:px-8">
                <div class="grid grid-cols-2 md:grid-cols-4 gap-8">
                    <div class="text-center">
                        <div class="text-3xl md:text-4xl font-bold text-white mb-2">25Gbps</div>
                        <div class="text-sky-400 font-medium text-sm">Max Bandwidth</div>
                        <div class="text-gray-400 text-xs">Guaranteed speed</div>
                    </div>
                    <div class="text-center">
                        <div class="text-3xl md:text-4xl font-bold text-white mb-2">512</div>
                        <div class="text-sky-400 font-medium text-sm">CPU Threads</div>
                        <div class="text-gray-400 text-xs">AMD EPYC power</div>
                    </div>
                    <div class="text-center">
                        <div class="text-3xl md:text-4xl font-bold text-white mb-2">1TB</div>
                        <div class="text-sky-400 font-medium text-sm">Max RAM</div>
                        <div class="text-gray-400 text-xs">DDR5 memory</div>
                    </div>
                    <div class="text-center">
                        <div class="text-3xl md:text-4xl font-bold text-white mb-2">24</div>
                        <div class="text-sky-400 font-medium text-sm">NVMe Slots</div>
                        <div class="text-gray-400 text-xs">Maximum capacity</div>
                    </div>
                </div>
            </div>
        </section>

        <!-- Server Configurator Section -->
        <section id="server-configurator" class="py-24">
            <div class="container mx-auto px-4 sm:px-6 lg:px-8">
                <div class="text-center mb-16">
                    <h2 class="text-4xl md:text-5xl font-bold text-white mb-6">Choose Your Configuration</h2>
                    <p class="text-xl text-gray-400 max-w-3xl mx-auto">
                        Select from our pre-configured dedicated streaming servers or build a custom solution for your specific needs.
                    </p>
                </div>

                <!-- All Servers Section -->
                <div class="mb-16">
                    <div class="text-center mb-12">
                        <h3 class="text-2xl font-bold text-white mb-2">Intel Xeon Dedicated Servers</h3>
                        <p class="text-gray-400 mb-8">High-performance Intel processors optimized for streaming and media delivery</p>
                    </div>
                    
                    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                        <!-- Xeon E5-2630v3 -->
                        <div class="group relative">
                            <div class="absolute inset-0 bg-gradient-to-r from-blue-500/10 to-cyan-600/10 rounded-2xl blur-xl transition-opacity group-hover:opacity-100 opacity-0"></div>
                            <div class="relative bg-slate-900/50 border border-slate-700/50 rounded-2xl p-6 transition-all duration-500 hover:border-blue-500/50 hover:bg-slate-800/50">
                                <div class="flex items-center justify-between mb-4">
                                    <div>
                                        <h4 class="text-xl font-bold text-white">Dual Xeon E5-2630v3</h4>
                                        <p class="text-blue-400 font-medium text-sm">Entry Level</p>
                                    </div>
                                    <div class="text-right">
                                        <div class="text-2xl font-bold text-white">€209</div>
                                        <div class="text-gray-400 text-xs">/month</div>
                                    </div>
                                </div>
                                
                                <div class="space-y-3 mb-6 text-sm">
                                    <div class="flex items-center justify-between py-1">
                                        <span class="text-gray-300">16c / 32t @ 2.4GHz</span>
                                        <span class="text-gray-300">128GB DDR4 @ 2133MHz</span>
                                    </div>
                                    <div class="flex items-center justify-between py-1">
                                        <span class="text-gray-300">6x SSD Slots</span>
                                        <span class="text-blue-400 font-semibold">1Gbps Guaranteed</span>
                                    </div>
                                    <div class="flex items-center justify-between py-1">
                                        <span class="text-gray-300">AI Optimized Routing</span>
                                        <span class="text-green-400">✓ Included</span>
                                    </div>
                                </div>

                                <div class="flex gap-3">
                                    <button class="flex-1 bg-gradient-to-r from-blue-500 to-sky-600 text-white px-4 py-2 rounded-lg font-semibold hover:from-blue-600 hover:to-sky-700 transition-all duration-300">
                                        Configure Now
                                    </button>
     
                                </div>
                            </div>
                        </div>

                        <!-- Xeon E5-2690v4 -->
                        <div class="group relative">
                            <div class="absolute inset-0 bg-gradient-to-r from-sky-500/10 to-blue-600/10 rounded-2xl blur-xl transition-opacity group-hover:opacity-100 opacity-0"></div>
                            <div class="relative bg-slate-900/50 border border-slate-700/50 rounded-2xl p-6 transition-all duration-500 hover:border-sky-500/50 hover:bg-slate-800/50">
                                <div class="flex items-center justify-between mb-4">
                                    <div>
                                        <h4 class="text-xl font-bold text-white">Dual Xeon E5-2690v4</h4>
                                        <p class="text-sky-400 font-medium text-sm">Popular Choice</p>
                                    </div>
                                    <div class="text-right">
                                        <div class="text-2xl font-bold text-white">€229</div>
                                        <div class="text-gray-400 text-xs">/month</div>
                                    </div>
                                </div>
                                
                                <div class="space-y-3 mb-6 text-sm">
                                    <div class="flex items-center justify-between py-1">
                                        <span class="text-gray-300">28c / 56t @ 2.6GHz</span>
                                        <span class="text-gray-300">128GB DDR4 @ 2133MHz</span>
                                    </div>
                                    <div class="flex items-center justify-between py-1">
                                        <span class="text-gray-300">6x SSD Slots</span>
                                        <span class="text-sky-400 font-semibold">1Gbps Guaranteed</span>
                                    </div>
                                    <div class="flex items-center justify-between py-1">
                                        <span class="text-gray-300">AI Optimized Routing</span>
                                        <span class="text-green-400">✓ Included</span>
                                    </div>
                                </div>

                                <div class="flex gap-3">
                                    <button class="flex-1 bg-gradient-to-r from-sky-500 to-blue-600 text-white px-4 py-2 rounded-lg font-semibold hover:from-sky-600 hover:to-blue-700 transition-all duration-300">
                                        Configure Now
                                    </button>
            
                                </div>
                            </div>
                        </div>

                        <!-- Xeon E5-2699v4 -->
                        <div class="group relative">
                            <div class="absolute inset-0 bg-gradient-to-r from-purple-500/10 to-pink-600/10 rounded-2xl blur-xl transition-opacity group-hover:opacity-100 opacity-0"></div>
                            <div class="relative bg-slate-900/50 border border-slate-700/50 rounded-2xl p-6 transition-all duration-500 hover:border-purple-500/50 hover:bg-slate-800/50">
                                <div class="absolute -top-3 left-1/2 transform -translate-x-1/2">
                                    <div class="bg-gradient-to-r from-purple-600 to-pink-600 text-white px-4 py-1 rounded-full text-xs font-bold">
                                        POPULAR
                                    </div>
                                </div>
                                
                                <div class="flex items-center justify-between mb-4 mt-2">
                                    <div>
                                        <h4 class="text-xl font-bold text-white">Dual Xeon E5-2699v4</h4>
                                        <p class="text-purple-400 font-medium text-sm">High Performance</p>
                                    </div>
                                    <div class="text-right">
                                        <div class="text-2xl font-bold text-white">€299</div>
                                        <div class="text-gray-400 text-xs">/month</div>
                                    </div>
                                </div>
                                
                                <div class="space-y-3 mb-6 text-sm">
                                    <div class="flex items-center justify-between py-1">
                                        <span class="text-gray-300">44c / 88t @ 2.2GHz</span>
                                        <span class="text-gray-300">256GB DDR4 @ 2133MHz</span>
                                    </div>
                                    <div class="flex items-center justify-between py-1">
                                        <span class="text-gray-300">6x SSD Slots</span>
                                        <span class="text-purple-400 font-semibold">1Gbps Guaranteed</span>
                                    </div>
                                    <div class="flex items-center justify-between py-1">
                                        <span class="text-gray-300">AI Optimized Routing</span>
                                        <span class="text-green-400">✓ Included</span>
                                    </div>
                                </div>
                                
                                <div class="flex gap-3">
                                    <button class="flex-1 bg-gradient-to-r from-purple-500 to-pink-600 text-white px-4 py-2 rounded-lg font-semibold hover:from-purple-600 hover:to-pink-700 transition-all duration-300">
                                        Configure Now
                                    </button>
  
                                </div>
                            </div>
                        </div>

                        <!-- Xeon Gold 6230 -->
                        <div class="group relative">
                            <div class="absolute inset-0 bg-gradient-to-r from-yellow-500/10 to-orange-600/10 rounded-2xl blur-xl transition-opacity group-hover:opacity-100 opacity-0"></div>
                            <div class="relative bg-slate-900/50 border border-slate-700/50 rounded-2xl p-6 transition-all duration-500 hover:border-yellow-500/50 hover:bg-slate-800/50">
                                <div class="flex items-center justify-between mb-4">
                                    <div>
                                        <h4 class="text-xl font-bold text-white">Dual Xeon Gold 6230</h4>
                                        <p class="text-yellow-400 font-medium text-sm">Premium Performance</p>
                                    </div>
                                    <div class="text-right">
                                        <div class="text-2xl font-bold text-white">€299</div>
                                        <div class="text-gray-400 text-xs">/month</div>
                                    </div>
                                </div>

                                <div class="space-y-3 mb-6 text-sm">
                                    <div class="flex items-center justify-between py-1">
                                        <span class="text-gray-300">40c / 80t @ 2.1GHz</span>
                                        <span class="text-gray-300">256GB DDR4 @ 2133MHz</span>
                                    </div>
                                    <div class="flex items-center justify-between py-1">
                                        <span class="text-gray-300">6x SSD Slots</span>
                                        <span class="text-yellow-400 font-semibold">1Gbps Guaranteed</span>
                                    </div>
                                    <div class="flex items-center justify-between py-1">
                                        <span class="text-gray-300">AI Optimized Routing</span>
                                        <span class="text-green-400">✓ Included</span>
                                    </div>
                                </div>

                                <div class="flex gap-3">
                                    <button class="flex-1 bg-gradient-to-r from-yellow-500 to-orange-600 text-white px-4 py-2 rounded-lg font-semibold hover:from-yellow-600 hover:to-orange-700 transition-all duration-300">
                                        Configure Now
                                    </button>
                     
                                </div>
                            </div>
                        </div>

                        <!-- Xeon Gold 6148 -->
                        <div class="group relative">
                            <div class="absolute inset-0 bg-gradient-to-r from-red-500/10 to-orange-600/10 rounded-2xl blur-xl transition-opacity group-hover:opacity-100 opacity-0"></div>
                            <div class="relative bg-slate-900/50 border border-slate-700/50 rounded-2xl p-6 transition-all duration-500 hover:border-red-500/50 hover:bg-slate-800/50">
                                <div class="flex items-center justify-between mb-4">
                                    <div>
                                        <h4 class="text-xl font-bold text-white">Dual Xeon Gold 6148</h4>
                                        <p class="text-red-400 font-medium text-sm">Ultimate Performance</p>
                                    </div>
                                    <div class="text-right">
                                        <div class="text-2xl font-bold text-white">€1799</div>
                                        <div class="text-gray-400 text-xs">/month</div>
                                    </div>
                                </div>
                                
                                <div class="space-y-3 mb-6 text-sm">
                                    <div class="flex items-center justify-between py-1">
                                        <span class="text-gray-300">40c / 80t @ 2.4GHz</span>
                                        <span class="text-gray-300">256GB DDR4 @ 2133MHz</span>
                                    </div>
                                    <div class="flex items-center justify-between py-1">
                                        <span class="text-gray-300">6x SSD Slots</span>
                                        <span class="text-red-400 font-semibold">25Gbps Guaranteed</span>
                                    </div>
                                    <div class="flex items-center justify-between py-1">
                                        <span class="text-gray-300">AI Optimized Routing</span>
                                        <span class="text-green-400">✓ Included</span>
                                    </div>
                                </div>

                                <div class="flex gap-3">
                                    <button class="flex-1 bg-gradient-to-r from-red-500 to-orange-600 text-white px-4 py-2 rounded-lg font-semibold hover:from-red-600 hover:to-orange-700 transition-all duration-300">
                                        Configure Now
                                    </button>

                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                    <!-- AMD EPYC Servers Section -->
                    <div class="text-center mb-8 mt-16">
                        <h3 class="text-2xl font-bold text-white mb-2">AMD EPYC Dedicated Servers</h3>
                        <p class="text-gray-400 mb-8">Next-generation AMD processors with DDR5 memory and high-performance computing</p>
                    </div>

                    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                        <!-- Single AMD EPYC 9254 -->
                        <div class="group relative">
                            <div class="absolute inset-0 bg-gradient-to-r from-emerald-500/10 to-teal-600/10 rounded-2xl blur-xl transition-opacity group-hover:opacity-100 opacity-0"></div>
                            <div class="relative bg-slate-900/50 border border-slate-700/50 rounded-2xl p-6 transition-all duration-500 hover:border-emerald-500/50 hover:bg-slate-800/50">
                                <div class="flex items-center justify-between mb-4">
                                    <div>
                                        <h4 class="text-xl font-bold text-white">Single EPYC 9254</h4>
                                        <p class="text-emerald-400 font-medium text-sm">Entry Level</p>
                                    </div>
                                    <div class="text-right">
                                        <div class="text-2xl font-bold text-white">€449</div>
                                        <div class="text-gray-400 text-xs">/month</div>
                                    </div>
                                </div>

                                <div class="space-y-3 mb-6 text-sm">
                                    <div class="flex items-center justify-between py-1">
                                        <span class="text-gray-300">24c / 48t @ 2.9GHz</span>
                                        <span class="text-gray-300">128GB DDR5 @ 4800MHz</span>
                                    </div>
                                    <div class="flex items-center justify-between py-1">
                                        <span class="text-gray-300">10x NVMe Slots</span>
                                        <span class="text-emerald-400 font-semibold">1Gbps Guaranteed</span>
                                    </div>
                                    <div class="flex items-center justify-between py-1">
                                        <span class="text-gray-300">AI Optimized Routing</span>
                                        <span class="text-green-400">✓ Included</span>
                                    </div>
                                </div>

                                <div class="flex gap-3">
                                    <button class="flex-1 bg-gradient-to-r from-emerald-500 to-teal-600 text-white px-4 py-2 rounded-lg font-semibold hover:from-emerald-600 hover:to-teal-700 transition-all duration-300">
                                        Configure Now
                                    </button>
  
                                </div>
                            </div>
                        </div>

                        <!-- Dual AMD EPYC 9254 -->
                        <div class="group relative">
                            <div class="absolute inset-0 bg-gradient-to-r from-cyan-500/10 to-blue-600/10 rounded-2xl blur-xl transition-opacity group-hover:opacity-100 opacity-0"></div>
                            <div class="relative bg-slate-900/50 border border-slate-700/50 rounded-2xl p-6 transition-all duration-500 hover:border-cyan-500/50 hover:bg-slate-800/50">
                                <div class="flex items-center justify-between mb-4">
                                    <div>
                                        <h4 class="text-xl font-bold text-white">Dual EPYC 9254</h4>
                                        <p class="text-cyan-400 font-medium text-sm">High Performance</p>
                                    </div>
                                    <div class="text-right">
                                        <div class="text-2xl font-bold text-white">€679</div>
                                        <div class="text-gray-400 text-xs">/month</div>
                                    </div>
                                </div>

                                <div class="space-y-3 mb-6 text-sm">
                                    <div class="flex items-center justify-between py-1">
                                        <span class="text-gray-300">48c / 96t @ 2.9GHz</span>
                                        <span class="text-gray-300">256GB DDR5 @ 4800MHz</span>
                                    </div>
                                    <div class="flex items-center justify-between py-1">
                                        <span class="text-gray-300">10x NVMe Slots</span>
                                        <span class="text-cyan-400 font-semibold">1Gbps Guaranteed</span>
                                    </div>
                                    <div class="flex items-center justify-between py-1">
                                        <span class="text-gray-300">AI Optimized Routing</span>
                                        <span class="text-green-400">✓ Included</span>
                                    </div>
                                </div>

                                <div class="flex gap-3">
                                    <button class="flex-1 bg-gradient-to-r from-cyan-500 to-blue-600 text-white px-4 py-2 rounded-lg font-semibold hover:from-cyan-600 hover:to-blue-700 transition-all duration-300">
                                        Configure Now
                                    </button>
 
                                </div>
                            </div>
                        </div>

                        <!-- Dual AMD EPYC 9554 -->
                        <div class="group relative">
                            <div class="absolute inset-0 bg-gradient-to-r from-violet-500/10 to-purple-600/10 rounded-2xl blur-xl transition-opacity group-hover:opacity-100 opacity-0"></div>
                            <div class="relative bg-slate-900/50 border border-slate-700/50 rounded-2xl p-6 transition-all duration-500 hover:border-violet-500/50 hover:bg-slate-800/50">
                                <div class="flex items-center justify-between mb-4">
                                    <div>
                                        <h4 class="text-xl font-bold text-white">Dual EPYC 9554</h4>
                                        <p class="text-violet-400 font-medium text-sm">Ultimate Performance</p>
                                    </div>
                                    <div class="text-right">
                                        <div class="text-2xl font-bold text-white">€959</div>
                                        <div class="text-gray-400 text-xs">/month</div>
                                    </div>
                                </div>

                                <div class="space-y-3 mb-6 text-sm">
                                    <div class="flex items-center justify-between py-1">
                                        <span class="text-gray-300">128c / 256t @ 3.1GHz</span>
                                        <span class="text-gray-300">512GB DDR5 @ 4800MHz</span>
                                    </div>
                                    <div class="flex items-center justify-between py-1">
                                        <span class="text-gray-300">24x NVMe Slots</span>
                                        <span class="text-violet-400 font-semibold">1Gbps Guaranteed</span>
                                    </div>
                                    <div class="flex items-center justify-between py-1">
                                        <span class="text-gray-300">AI Optimized Routing</span>
                                        <span class="text-green-400">✓ Included</span>
                                    </div>
                                </div>

                                <div class="flex gap-3">
                                    <button class="flex-1 bg-gradient-to-r from-violet-500 to-purple-600 text-white px-4 py-2 rounded-lg font-semibold hover:from-violet-600 hover:to-purple-700 transition-all duration-300">
                                        Configure Now
                                    </button>
   
                                </div>
                            </div>
                        </div>

                        <!-- Dual AMD EPYC 9754 -->
                        <div class="group relative">
                            <div class="absolute inset-0 bg-gradient-to-r from-red-500/10 to-pink-600/10 rounded-2xl blur-xl transition-opacity group-hover:opacity-100 opacity-0"></div>
                            <div class="relative bg-slate-900/50 border border-slate-700/50 rounded-2xl p-6 transition-all duration-500 hover:border-red-500/50 hover:bg-slate-800/50">
                                <div class="flex items-center justify-between mb-4">
                                    <div>
                                        <h4 class="text-xl font-bold text-white">Dual EPYC 9754</h4>
                                        <p class="text-red-400 font-medium text-sm">Maximum Performance</p>
                                    </div>
                                    <div class="text-right">
                                        <div class="text-2xl font-bold text-white">€3314</div>
                                        <div class="text-gray-400 text-xs">/month</div>
                                    </div>
                                </div>

                                <div class="space-y-3 mb-6 text-sm">
                                    <div class="flex items-center justify-between py-1">
                                        <span class="text-gray-300">256c / 512t @ 2.3GHz</span>
                                        <span class="text-gray-300">1024GB DDR5 @ 4800MHz</span>
                                    </div>
                                    <div class="flex items-center justify-between py-1">
                                        <span class="text-gray-300">24x NVMe Slots</span>
                                        <span class="text-red-400 font-semibold">25Gbps Guaranteed</span>
                                    </div>
                                    <div class="flex items-center justify-between py-1">
                                        <span class="text-gray-300">AI Optimized Routing</span>
                                        <span class="text-green-400">✓ Included</span>
                                    </div>
                                </div>

                                <div class="flex gap-3">
                                    <button class="flex-1 bg-gradient-to-r from-red-500 to-pink-600 text-white px-4 py-2 rounded-lg font-semibold hover:from-red-600 hover:to-pink-700 transition-all duration-300">
                                        Configure Now
                                    </button>
               
                                </div>
                            </div>
                        </div>
                    </div>
                </div>


            </div>
        </section>

                <!-- Server Comparison Table -->
        <section id="server-comparison" class="py-24">
            <div class="container mx-auto px-4 sm:px-6 lg:px-8">
                <div class="text-center mb-16">
                    <h2 class="text-4xl md:text-5xl font-bold text-white mb-6">Complete Server Comparison</h2>
                    <p class="text-xl text-gray-400 max-w-3xl mx-auto">
                        Compare all our dedicated server configurations side by side to find the perfect match for your needs.
                    </p>
                </div>

                <!-- Unified Comparison Table -->
                <div class="bg-slate-900/30 rounded-3xl p-8 border border-slate-700/50 overflow-x-auto">
                    <table class="w-full text-sm">
                        <thead>
                            <tr class="border-b-2 border-slate-700">
                                <th class="text-left py-4 px-2 text-gray-300 font-semibold min-w-[120px]">Specifications</th>
                                <!-- Intel Xeon Servers -->
                                <th class="text-center py-4 px-2 text-blue-400 font-bold text-xs min-w-[100px]">E5-2630v3<br><span class="text-gray-400 font-normal">€209/mo</span></th>
                                <th class="text-center py-4 px-2 text-sky-400 font-bold text-xs min-w-[100px]">E5-2690v4<br><span class="text-gray-400 font-normal">€229/mo</span></th>
                                <th class="text-center py-4 px-2 text-purple-400 font-bold text-xs min-w-[100px] relative">
                                    E5-2699v4<br><span class="text-gray-400 font-normal">€299/mo</span>
                                    <div class="absolute -top-2 left-1/2 transform -translate-x-1/2">
                                        <span class="bg-purple-600 text-white px-2 py-1 rounded text-xs font-bold">POPULAR</span>
                                    </div>
                                </th>
                                <th class="text-center py-4 px-2 text-yellow-400 font-bold text-xs min-w-[100px]">Gold 6230<br><span class="text-gray-400 font-normal">€309/mo</span></th>
                                <th class="text-center py-4 px-2 text-red-400 font-bold text-xs min-w-[100px]">Gold 6148<br><span class="text-gray-400 font-normal">€1799/mo</span></th>
                                <!-- AMD EPYC Servers -->
                                <th class="text-center py-4 px-2 text-emerald-400 font-bold text-xs min-w-[100px]">EPYC 9254<br><span class="text-gray-400 font-normal">€449/mo</span></th>
                                <th class="text-center py-4 px-2 text-cyan-400 font-bold text-xs min-w-[100px]">Dual 9254<br><span class="text-gray-400 font-normal">€679/mo</span></th>
                                <th class="text-center py-4 px-2 text-violet-400 font-bold text-xs min-w-[100px]">
                                    Dual 9554<br><span class="text-gray-400 font-normal">€959/mo</span>
                                </th>
                                <th class="text-center py-4 px-2 text-red-400 font-bold text-xs min-w-[100px] relative">
                                    Dual 9754<br><span class="text-gray-400 font-normal">€3314/mo</span>
                                    <div class="absolute -top-2 left-1/2 transform -translate-x-1/2">
                                        <span class="bg-red-600 text-white px-2 py-1 rounded text-xs font-bold">ULTIMATE</span>
                                    </div>
                                </th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr class="border-b border-slate-800/50">
                                <td class="py-3 px-2 text-gray-300 font-medium">Processor Type</td>
                                <td class="py-3 px-2 text-center text-white text-xs">Intel Xeon</td>
                                <td class="py-3 px-2 text-center text-white text-xs">Intel Xeon</td>
                                <td class="py-3 px-2 text-center text-white text-xs">Intel Xeon</td>
                                <td class="py-3 px-2 text-center text-white text-xs">Intel Xeon</td>
                                <td class="py-3 px-2 text-center text-white text-xs">Intel Xeon</td>
                                <td class="py-3 px-2 text-center text-white text-xs">AMD EPYC</td>
                                <td class="py-3 px-2 text-center text-white text-xs">AMD EPYC</td>
                                <td class="py-3 px-2 text-center text-white text-xs">AMD EPYC</td>
                                <td class="py-3 px-2 text-center text-white text-xs">AMD EPYC</td>
                            </tr>
                            <tr class="border-b border-slate-800/50">
                                <td class="py-3 px-2 text-gray-300 font-medium">Cores / Threads</td>
                                <td class="py-3 px-2 text-center text-blue-400 font-bold text-xs">16c / 32t</td>
                                <td class="py-3 px-2 text-center text-sky-400 font-bold text-xs">28c / 56t</td>
                                <td class="py-3 px-2 text-center text-purple-400 font-bold text-xs">40c / 80t</td>
                                <td class="py-3 px-2 text-center text-yellow-400 font-bold text-xs">40c / 80t</td>
                                <td class="py-3 px-2 text-center text-red-400 font-bold text-xs">40c / 80t</td>
                                <td class="py-3 px-2 text-center text-emerald-400 font-bold text-xs">24c / 48t</td>
                                <td class="py-3 px-2 text-center text-cyan-400 font-bold text-xs">48c / 96t</td>
                                <td class="py-3 px-2 text-center text-violet-400 font-bold text-xs">128c / 256t</td>
                                <td class="py-3 px-2 text-center text-red-400 font-bold text-xs">256c / 512t</td>
                            </tr>
                            <tr class="border-b border-slate-800/50">
                                <td class="py-3 px-2 text-gray-300 font-medium">RAM Memory</td>
                                <td class="py-3 px-2 text-center text-white text-xs">128 GB DDR4</td>
                                <td class="py-3 px-2 text-center text-white text-xs">128 GB DDR4</td>
                                <td class="py-3 px-2 text-center text-white text-xs">256 GB DDR4</td>
                                <td class="py-3 px-2 text-center text-white text-xs">256 GB DDR4</td>
                                <td class="py-3 px-2 text-center text-white text-xs">256 GB DDR4</td>
                                <td class="py-3 px-2 text-center text-white text-xs">128 GB DDR5</td>
                                <td class="py-3 px-2 text-center text-white text-xs">256 GB DDR5</td>
                                <td class="py-3 px-2 text-center text-white text-xs">512 GB DDR5</td>
                                <td class="py-3 px-2 text-center text-white text-xs">1024 GB DDR5</td>
                            </tr>
                            <tr class="border-b border-slate-800/50">
                                <td class="py-3 px-2 text-gray-300 font-medium">Storage</td>
                                <td class="py-3 px-2 text-center text-white text-xs">240GB SSD</td>
                                <td class="py-3 px-2 text-center text-white text-xs">240GB SSD</td>
                                <td class="py-3 px-2 text-center text-white text-xs">240GB SSD</td>
                                <td class="py-3 px-2 text-center text-white text-xs">240GB SSD</td>
                                <td class="py-3 px-2 text-center text-white text-xs">240GB SSD</td>
                                <td class="py-3 px-2 text-center text-white text-xs">10x NVMe</td>
                                <td class="py-3 px-2 text-center text-white text-xs">10x NVMe</td>
                                <td class="py-3 px-2 text-center text-white text-xs">24x NVMe</td>
                                <td class="py-3 px-2 text-center text-white text-xs">24x NVMe</td>
                            </tr>
                            <tr class="border-b border-slate-800/50">
                                <td class="py-3 px-2 text-gray-300 font-medium">Bandwidth</td>
                                <td class="py-3 px-2 text-center text-white text-xs">1/2/5/10 Gbps</td>
                                <td class="py-3 px-2 text-center text-white text-xs">1/2/5/10 Gbps</td>
                                <td class="py-3 px-2 text-center text-white text-xs">1/2/5/10/20 Gbps</td>
                                <td class="py-3 px-2 text-center text-white text-xs">1/2/5/10/20 Gbps</td>
                                <td class="py-3 px-2 text-center text-white text-xs">25/50/100 Gbps</td>
                                <td class="py-3 px-2 text-center text-white text-xs">1/2/5/10 Gbps</td>
                                <td class="py-3 px-2 text-center text-white text-xs">1/2/5/10 Gbps</td>
                                <td class="py-3 px-2 text-center text-white text-xs">1/2/5/10 Gbps</td>
                                <td class="py-3 px-2 text-center text-white text-xs">25Gbps Guaranteed</td>
                            </tr>
                            <tr class="border-b border-slate-800/50">
                                <td class="py-3 px-2 text-gray-300 font-medium">Available Locations</td>
                                <td class="py-3 px-2 text-center text-white text-xs">🇷🇴🇮🇪🇮🇹🇦🇹🇩🇪🇳🇱🇧🇬🇪🇸🇵🇹🇫🇷🇵🇱🇺🇸</td>
                                <td class="py-3 px-2 text-center text-white text-xs">🇷🇴🇮🇪🇮🇹🇦🇹🇩🇪🇳🇱🇧🇬🇪🇸🇵🇹🇫🇷🇵🇱🇺🇸</td>
                                <td class="py-3 px-2 text-center text-white text-xs">🇷🇴🇮🇪🇮🇹🇦🇹🇩🇪🇳🇱🇧🇬🇪🇸🇵🇹🇫🇷🇵🇱🇺🇸</td>
                                <td class="py-3 px-2 text-center text-white text-xs">🇷🇴🇮🇪🇮🇹🇦🇹🇩🇪🇳🇱🇧🇬🇪🇸🇵🇹🇫🇷🇵🇱🇺🇸</td>
                                <td class="py-3 px-2 text-center text-white text-xs">🇷🇴🇮🇪🇮🇹🇦🇹🇩🇪🇳🇱🇧🇬🇪🇸🇵🇹🇫🇷🇵🇱🇺🇸</td>
                                <td class="py-3 px-2 text-center text-white text-xs">🇷🇴🇮🇪🇮🇹🇦🇹🇩🇪🇳🇱🇧🇬🇪🇸🇵🇹🇫🇷🇵🇱🇺🇸</td>
                                <td class="py-3 px-2 text-center text-white text-xs">🇷🇴🇮🇪🇮🇹🇦🇹🇩🇪🇳🇱🇧🇬🇪🇸🇵🇹🇫🇷🇵🇱🇺🇸</td>
                                <td class="py-3 px-2 text-center text-white text-xs">🇷🇴🇮🇪🇮🇹🇦🇹🇩🇪🇳🇱🇧🇬🇪🇸🇵🇹🇫🇷🇵🇱🇺🇸</td>
                                <td class="py-3 px-2 text-center text-white text-xs">🇷🇴🇮🇪🇮🇹🇦🇹🇩🇪🇳🇱🇧🇬🇪🇸🇵🇹🇫🇷🇵🇱🇺🇸</td>
                            </tr>
                            <tr class="border-b border-slate-800/50">
                                <td class="py-3 px-2 text-gray-300 font-medium">Network Routing</td>
                                <td class="py-3 px-2 text-center text-green-400 text-xs">AI Optimized</td>
                                <td class="py-3 px-2 text-center text-green-400 text-xs">AI Optimized</td>
                                <td class="py-3 px-2 text-center text-green-400 text-xs">AI Optimized</td>
                                <td class="py-3 px-2 text-center text-green-400 text-xs">AI Optimized</td>
                                <td class="py-3 px-2 text-center text-green-400 text-xs">AI Optimized</td>
                                <td class="py-3 px-2 text-center text-green-400 text-xs">AI Optimized</td>
                                <td class="py-3 px-2 text-center text-green-400 text-xs">AI Optimized</td>
                                <td class="py-3 px-2 text-center text-green-400 text-xs">AI Optimized</td>
                                <td class="py-3 px-2 text-center text-green-400 text-xs">AI Optimized</td>
                            </tr>
                            <tr>
                                <td class="py-3 px-2 text-gray-300 font-medium">Deployment</td>
                                <td class="py-3 px-2 text-center text-green-400 text-xs">1 Hour</td>
                                <td class="py-3 px-2 text-center text-green-400 text-xs">1 Hour</td>
                                <td class="py-3 px-2 text-center text-green-400 text-xs">1 Hour</td>
                                <td class="py-3 px-2 text-center text-green-400 text-xs">1 Hour</td>
                                <td class="py-3 px-2 text-center text-green-400 text-xs">1 Hour</td>
                                <td class="py-3 px-2 text-center text-green-400 text-xs">1 Hour</td>
                                <td class="py-3 px-2 text-center text-green-400 text-xs">1 Hour</td>
                                <td class="py-3 px-2 text-center text-green-400 text-xs">1 Hour</td>
                                <td class="py-3 px-2 text-center text-green-400 text-xs">1 Hour</td>
                            </tr>
                        </tbody>
                    </table>
                </div>
                
                <!-- Custom Configuration CTA -->
                <div class="text-center bg-gradient-to-r from-slate-800/50 to-slate-700/50 rounded-2xl p-12 border border-slate-600/50 mt-16">
                    <h3 class="text-2xl font-bold text-white mb-4">Need a Custom Configuration?</h3>
                    <p class="text-gray-300 mb-6 max-w-2xl mx-auto">
                        Our enterprise team can design a custom dedicated server solution tailored to your specific streaming and hosting requirements.
                    </p>
                    <button class="btn-secondary px-8 py-3 rounded-lg font-semibold inline-flex items-center">
                        <i data-lucide="phone" class="w-4 h-4 mr-2"></i>
                        Contact Enterprise Team
                    </button>
                </div>
            </div>
        </section>

        <!-- Global Network Section -->
        <section class="py-24 bg-slate-950/30">
            <div class="container mx-auto px-4 sm:px-6 lg:px-8">
                <div class="text-center mb-12">
                    <h2 class="text-3xl md:text-4xl font-bold text-white mb-4">Global Server Locations</h2>
                    <p class="text-gray-400">Premium datacenters across Europe and US</p>
                </div>

                <!-- Slider Container -->
                <div class="relative overflow-hidden">
                    <div id="location-slider" class="flex transition-transform duration-500 ease-in-out">
                        <!-- Slide 1 - Western Europe -->
                        <div class="min-w-full">
                            <div class="grid grid-cols-2 md:grid-cols-4 gap-4 w-full">
                                <div class="bg-slate-800/50 p-4 rounded-lg text-center hover:bg-slate-700/50 transition-colors">
                                    <div class="text-2xl mb-2">��</div>
                                    <h5 class="text-white text-sm font-semibold">Frankfurt</h5>
                                    <p class="text-gray-400 text-xs">Germany</p>
                                </div>
                                <div class="bg-slate-800/50 p-4 rounded-lg text-center hover:bg-slate-700/50 transition-colors">
                                    <div class="text-2xl mb-2">🇳🇱</div>
                                    <h5 class="text-white text-sm font-semibold">Amsterdam</h5>
                                    <p class="text-gray-400 text-xs">Netherlands</p>
                                </div>
                                <div class="bg-slate-800/50 p-4 rounded-lg text-center hover:bg-slate-700/50 transition-colors">
                                    <div class="text-2xl mb-2">🇫🇷</div>
                                    <h5 class="text-white text-sm font-semibold">Paris</h5>
                                    <p class="text-gray-400 text-xs">France</p>
                                </div>
                                <div class="bg-slate-800/50 p-4 rounded-lg text-center hover:bg-slate-700/50 transition-colors">
                                    <div class="text-2xl mb-2">��</div>
                                    <h5 class="text-white text-sm font-semibold">Marseille</h5>
                                    <p class="text-gray-400 text-xs">France</p>
                                </div>
                            </div>
                        </div>

                        <!-- Slide 2 - Southern Europe -->
                        <div class="min-w-full">
                            <div class="grid grid-cols-2 md:grid-cols-4 gap-4 w-full">
                                <div class="bg-slate-800/50 p-4 rounded-lg text-center hover:bg-slate-700/50 transition-colors">
                                    <div class="text-2xl mb-2">🇮🇹</div>
                                    <h5 class="text-white text-sm font-semibold">Milano</h5>
                                    <p class="text-gray-400 text-xs">Italy</p>
                                </div>
                                <div class="bg-slate-800/50 p-4 rounded-lg text-center hover:bg-slate-700/50 transition-colors">
                                    <div class="text-2xl mb-2">🇸</div>
                                    <h5 class="text-white text-sm font-semibold">Madrid</h5>
                                    <p class="text-gray-400 text-xs">Spain</p>
                                </div>
                                <div class="bg-slate-800/50 p-4 rounded-lg text-center hover:bg-slate-700/50 transition-colors">
                                    <div class="text-2xl mb-2">🇵🇹</div>
                                    <h5 class="text-white text-sm font-semibold">Lisbon</h5>
                                    <p class="text-gray-400 text-xs">Portugal</p>
                                </div>
                                <div class="bg-slate-800/50 p-4 rounded-lg text-center hover:bg-slate-700/50 transition-colors">
                                    <div class="text-2xl mb-2">��</div>
                                    <h5 class="text-white text-sm font-semibold">Vienna</h5>
                                    <p class="text-gray-400 text-xs">Austria</p>
                                </div>
                            </div>
                        </div>

                        <!-- Slide 3 - Eastern Europe & Ireland -->
                        <div class="min-w-full">
                            <div class="grid grid-cols-2 md:grid-cols-4 gap-4 w-full">
                                <div class="bg-slate-800/50 p-4 rounded-lg text-center hover:bg-slate-700/50 transition-colors">
                                    <div class="text-2xl mb-2">🇴</div>
                                    <h5 class="text-white text-sm font-semibold">Bucharest</h5>
                                    <p class="text-gray-400 text-xs">Romania</p>
                                </div>
                                <div class="bg-slate-800/50 p-4 rounded-lg text-center hover:bg-slate-700/50 transition-colors">
                                    <div class="text-2xl mb-2">🇧🇬</div>
                                    <h5 class="text-white text-sm font-semibold">Sofia</h5>
                                    <p class="text-gray-400 text-xs">Bulgaria</p>
                                </div>
                                <div class="bg-slate-800/50 p-4 rounded-lg text-center hover:bg-slate-700/50 transition-colors">
                                    <div class="text-2xl mb-2">��</div>
                                    <h5 class="text-white text-sm font-semibold">Warsaw</h5>
                                    <p class="text-gray-400 text-xs">Poland</p>
                                </div>
                                <div class="bg-slate-800/50 p-4 rounded-lg text-center hover:bg-slate-700/50 transition-colors">
                                    <div class="text-2xl mb-2">🇮🇪</div>
                                    <h5 class="text-white text-sm font-semibold">Dublin</h5>
                                    <p class="text-gray-400 text-xs">Ireland</p>
                                </div>
                            </div>
                        </div>

                        <!-- Slide 4 - United States -->
                        <div class="min-w-full">
                            <div class="grid grid-cols-2 md:grid-cols-4 gap-4 w-full">
                                <div class="bg-slate-800/50 p-4 rounded-lg text-center hover:bg-slate-700/50 transition-colors">
                                    <div class="text-2xl mb-2">🇺🇸</div>
                                    <h5 class="text-white text-sm font-semibold">Ashburn</h5>
                                    <p class="text-gray-400 text-xs">United States</p>
                                </div>
                                <div class="bg-slate-800/50 p-4 rounded-lg text-center hover:bg-slate-700/50 transition-colors opacity-50">
                                    <div class="text-2xl mb-2">�</div>
                                    <h5 class="text-white text-sm font-semibold">More Coming</h5>
                                    <p class="text-gray-400 text-xs">Soon</p>
                                </div>
                                <div class="bg-slate-800/50 p-4 rounded-lg text-center hover:bg-slate-700/50 transition-colors opacity-50">
                                    <div class="text-2xl mb-2">🌐</div>
                                    <h5 class="text-white text-sm font-semibold">Global</h5>
                                    <p class="text-gray-400 text-xs">Expansion</p>
                                </div>
                                <div class="bg-slate-800/50 p-4 rounded-lg text-center hover:bg-slate-700/50 transition-colors opacity-50">
                                    <div class="text-2xl mb-2">�</div>
                                    <h5 class="text-white text-sm font-semibold">Network</h5>
                                    <p class="text-gray-400 text-xs">Growth</p>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Navigation Dots -->
                    <div class="flex justify-center mt-6 space-x-2">
                        <button class="location-dot w-3 h-3 rounded-full bg-sky-400 opacity-100"></button>
                        <button class="location-dot w-3 h-3 rounded-full bg-gray-400 opacity-50"></button>
                        <button class="location-dot w-3 h-3 rounded-full bg-gray-400 opacity-50"></button>
                        <button class="location-dot w-3 h-3 rounded-full bg-gray-400 opacity-50"></button>
                    </div>
                </div>
            </div>
        </section>


        <!-- Enterprise Features -->
        <section class="py-24 bg-slate-950/50">
            <div class="container mx-auto px-4 sm:px-6 lg:px-8">
                <div class="text-center mb-16">
                    <h2 class="text-4xl md:text-5xl font-bold text-white mb-6">Enterprise-Grade Infrastructure</h2>
                    <p class="text-xl text-gray-400 max-w-3xl mx-auto">
                        Built for mission-critical applications with enterprise features and 24/7 expert support.
                    </p>
                </div>

                <!-- Enterprise Features Grid -->
                <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8 mb-16">
                    <div class="bg-slate-800/30 rounded-2xl p-8 border border-slate-700/50 hover:border-blue-500/50 transition-all duration-300 group">
                        <div class="w-16 h-16 bg-gradient-to-br from-blue-500 to-sky-600 rounded-xl flex items-center justify-center mb-6 group-hover:scale-110 transition-transform">
                            <i data-lucide="shield-check" class="w-8 h-8 text-white"></i>
                        </div>
                        <h3 class="text-xl font-bold text-white mb-4">Enterprise Security</h3>
                        <ul class="text-gray-400 text-sm space-y-2">
                            <li class="flex items-center"><i data-lucide="check" class="w-4 h-4 mr-2 text-green-400"></i>Hardware Firewalls</li>
                            <li class="flex items-center"><i data-lucide="check" class="w-4 h-4 mr-2 text-green-400"></i>DDoS Protection</li>
                            <li class="flex items-center"><i data-lucide="check" class="w-4 h-4 mr-2 text-green-400"></i>Secure Remote Access</li>
                            <li class="flex items-center"><i data-lucide="check" class="w-4 h-4 mr-2 text-green-400"></i>SSL Certificates</li>
                        </ul>
                    </div>

                    <div class="bg-slate-800/30 rounded-2xl p-8 border border-slate-700/50 hover:border-green-500/50 transition-all duration-300 group">
                        <div class="w-16 h-16 bg-gradient-to-br from-green-500 to-emerald-600 rounded-xl flex items-center justify-center mb-6 group-hover:scale-110 transition-transform">
                            <i data-lucide="activity" class="w-8 h-8 text-white"></i>
                        </div>
                        <h3 class="text-xl font-bold text-white mb-4">24/7 Monitoring</h3>
                        <ul class="text-gray-400 text-sm space-y-2">
                            <li class="flex items-center"><i data-lucide="check" class="w-4 h-4 mr-2 text-green-400"></i>Real-time Alerts</li>
                            <li class="flex items-center"><i data-lucide="check" class="w-4 h-4 mr-2 text-green-400"></i>Performance Metrics</li>
                            <li class="flex items-center"><i data-lucide="check" class="w-4 h-4 mr-2 text-green-400"></i>Proactive Maintenance</li>
                            <li class="flex items-center"><i data-lucide="check" class="w-4 h-4 mr-2 text-green-400"></i>Custom Dashboards</li>
                        </ul>
                    </div>

                    <div class="bg-slate-800/30 rounded-2xl p-8 border border-slate-700/50 hover:border-purple-500/50 transition-all duration-300 group">
                        <div class="w-16 h-16 bg-gradient-to-br from-purple-500 to-pink-600 rounded-xl flex items-center justify-center mb-6 group-hover:scale-110 transition-transform">
                            <i data-lucide="headphones" class="w-8 h-8 text-white"></i>
                        </div>
                        <h3 class="text-xl font-bold text-white mb-4">Expert Support</h3>
                        <ul class="text-gray-400 text-sm space-y-2">
                            <li class="flex items-center"><i data-lucide="check" class="w-4 h-4 mr-2 text-green-400"></i>24/7 Availability</li>
                            <li class="flex items-center"><i data-lucide="check" class="w-4 h-4 mr-2 text-green-400"></i>Streaming Experts</li>
                            <li class="flex items-center"><i data-lucide="check" class="w-4 h-4 mr-2 text-green-400"></i>Priority Response</li>
                            <li class="flex items-center"><i data-lucide="check" class="w-4 h-4 mr-2 text-green-400"></i>Multiple Channels</li>
                        </ul>
                    </div>

                    <div class="bg-slate-800/30 rounded-2xl p-8 border border-slate-700/50 hover:border-orange-500/50 transition-all duration-300 group">
                        <div class="w-16 h-16 bg-gradient-to-br from-orange-500 to-red-600 rounded-xl flex items-center justify-center mb-6 group-hover:scale-110 transition-transform">
                            <i data-lucide="zap" class="w-8 h-8 text-white"></i>
                        </div>
                        <h3 class="text-xl font-bold text-white mb-4">Rapid Deployment</h3>
                        <ul class="text-gray-400 text-sm space-y-2">
                            <li class="flex items-center"><i data-lucide="check" class="w-4 h-4 mr-2 text-green-400"></i>1 Hour Setup</li>
                            <li class="flex items-center"><i data-lucide="check" class="w-4 h-4 mr-2 text-green-400"></i>Automated Provisioning</li>
                            <li class="flex items-center"><i data-lucide="check" class="w-4 h-4 mr-2 text-green-400"></i>Custom Configurations</li>
                            <li class="flex items-center"><i data-lucide="check" class="w-4 h-4 mr-2 text-green-400"></i>OS Pre-installation</li>
                        </ul>
                    </div>

                    <div class="bg-slate-800/30 rounded-2xl p-8 border border-slate-700/50 hover:border-cyan-500/50 transition-all duration-300 group">
                        <div class="w-16 h-16 bg-gradient-to-br from-cyan-500 to-blue-600 rounded-xl flex items-center justify-center mb-6 group-hover:scale-110 transition-transform">
                            <i data-lucide="server" class="w-8 h-8 text-white"></i>
                        </div>
                        <h3 class="text-xl font-bold text-white mb-4">Premium Hardware</h3>
                        <ul class="text-gray-400 text-sm space-y-2">
                            <li class="flex items-center"><i data-lucide="check" class="w-4 h-4 mr-2 text-green-400"></i>Enterprise SSDs</li>
                            <li class="flex items-center"><i data-lucide="check" class="w-4 h-4 mr-2 text-green-400"></i>ECC Memory</li>
                            <li class="flex items-center"><i data-lucide="check" class="w-4 h-4 mr-2 text-green-400"></i>Redundant Power</li>
                            <li class="flex items-center"><i data-lucide="check" class="w-4 h-4 mr-2 text-green-400"></i>RAID Controllers</li>
                        </ul>
                    </div>

                    <div class="bg-slate-800/30 rounded-2xl p-8 border border-slate-700/50 hover:border-yellow-500/50 transition-all duration-300 group">
                        <div class="w-16 h-16 bg-gradient-to-br from-yellow-500 to-orange-600 rounded-xl flex items-center justify-center mb-6 group-hover:scale-110 transition-transform">
                            <i data-lucide="globe" class="w-8 h-8 text-white"></i>
                        </div>
                        <h3 class="text-xl font-bold text-white mb-4">Global Network</h3>
                        <ul class="text-gray-400 text-sm space-y-2">
                            <li class="flex items-center"><i data-lucide="check" class="w-4 h-4 mr-2 text-green-400"></i>Tier-1 Providers</li>
                            <li class="flex items-center"><i data-lucide="check" class="w-4 h-4 mr-2 text-green-400"></i>Low Latency Routing</li>
                            <li class="flex items-center"><i data-lucide="check" class="w-4 h-4 mr-2 text-green-400"></i>BGP Optimization</li>
                            <li class="flex items-center"><i data-lucide="check" class="w-4 h-4 mr-2 text-green-400"></i>Multiple Uplinks</li>
                        </ul>
                    </div>
                </div>

                <!-- SLA Guarantee -->
                <div class="mt-16 bg-gradient-to-r from-slate-800/30 to-slate-700/50 rounded-3xl p-12 border border-slate-600/50 text-center">
                    <div class="max-w-4xl mx-auto">
                        <h3 class="text-3xl font-bold text-white mb-6">99.9% Uptime SLA Guarantee</h3>
                        <p class="text-xl text-gray-300 mb-8">
                            We guarantee 99.9% uptime with full SLA coverage. If we don't meet our commitment, you receive service credits automatically.
                        </p>
                        <div class="grid grid-cols-1 md:grid-cols-3 gap-8">
                            <div>
                                <div class="text-3xl font-bold text-sky-400 mb-2">99.9%</div>
                                <div class="text-gray-300">Uptime SLA</div>
                            </div>
                            <div>
                                <div class="text-3xl font-bold text-green-400 mb-2">< 1hr</div>
                                <div class="text-gray-300">Response Time</div>
                            </div>
                            <div>
                                <div class="text-3xl font-bold text-purple-400 mb-2">24/7</div>
                                <div class="text-gray-300">Expert Support</div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <!-- CTA Section -->
        <section class="py-24 bg-gradient-to-br from-slate-950 via-slate-900 to-slate-950">
            <div class="container mx-auto px-4 sm:px-6 lg:px-8 text-center">
                <h2 class="text-4xl md:text-5xl font-bold text-white mb-6">Ready to Get Started?</h2>
                <p class="text-xl text-gray-300 mb-12 max-w-3xl mx-auto">
                    Configure your dedicated streaming server and get online in under 1 hour. Our team is ready to help you scale your streaming infrastructure.
                </p>
                <div class="flex flex-col sm:flex-row gap-6 justify-center">
                    <a href="#server-configurator" class="btn-primary px-12 py-4 rounded-xl font-bold text-xl inline-flex items-center justify-center">
                        <i data-lucide="play-circle" class="w-6 h-6 mr-3"></i>
                        Configure Server Now
                    </a>
                    <a href="#" class="btn-secondary px-12 py-4 rounded-xl font-bold text-xl inline-flex items-center justify-center">
                        <i data-lucide="phone-call" class="w-6 h-6 mr-3"></i>
                        Talk to Expert
                    </a>
                </div>
            </div>
        </section>
    </main>

    <!-- Footer Placeholder -->
    <div id="footer-placeholder"></div>

    <script>
        // Animated gradient lines background
        const linesCanvas = document.getElementById('lines-canvas');
        if (linesCanvas) {
            const ctx = linesCanvas.getContext('2d');
            const heroSection = linesCanvas.parentElement;
            let linesArray = [];
            let animationId;

            function setCanvasSize() {
                linesCanvas.width = heroSection.offsetWidth;
                linesCanvas.height = heroSection.offsetHeight;
            }

            class AnimatedLine {
                constructor() {
                    this.reset();
                    this.gradientColors = [
                        ['rgba(14, 165, 233, 0.8)', 'rgba(56, 189, 248, 0.4)'], // sky blue
                        ['rgba(168, 85, 247, 0.8)', 'rgba(196, 181, 253, 0.4)'], // purple
                        ['rgba(34, 197, 94, 0.8)', 'rgba(134, 239, 172, 0.4)'], // green
                        ['rgba(239, 68, 68, 0.8)', 'rgba(252, 165, 165, 0.4)'], // red
                        ['rgba(245, 158, 11, 0.8)', 'rgba(253, 230, 138, 0.4)'], // amber
                        ['rgba(236, 72, 153, 0.8)', 'rgba(251, 207, 232, 0.4)'], // pink
                    ];
                    this.colorIndex = Math.floor(Math.random() * this.gradientColors.length);
                }

                reset() {
                    this.x1 = Math.random() * linesCanvas.width;
                    this.y1 = Math.random() * linesCanvas.height;
                    this.x2 = Math.random() * linesCanvas.width;
                    this.y2 = Math.random() * linesCanvas.height;
                    this.dx1 = (Math.random() - 0.5) * 0.5;
                    this.dy1 = (Math.random() - 0.5) * 0.5;
                    this.dx2 = (Math.random() - 0.5) * 0.5;
                    this.dy2 = (Math.random() - 0.5) * 0.5;
                    this.opacity = Math.random() * 0.5 + 0.2;
                    this.fadeDirection = Math.random() > 0.5 ? 1 : -1;
                    this.lineWidth = Math.random() * 1.5 + 0.5;
                }

                update() {
                    // Move line endpoints
                    this.x1 += this.dx1;
                    this.y1 += this.dy1;
                    this.x2 += this.dx2;
                    this.y2 += this.dy2;

                    // Bounce off edges
                    if (this.x1 <= 0 || this.x1 >= linesCanvas.width) this.dx1 *= -1;
                    if (this.y1 <= 0 || this.y1 >= linesCanvas.height) this.dy1 *= -1;
                    if (this.x2 <= 0 || this.x2 >= linesCanvas.width) this.dx2 *= -1;
                    if (this.y2 <= 0 || this.y2 >= linesCanvas.height) this.dy2 *= -1;

                    // Animate opacity
                    this.opacity += this.fadeDirection * 0.005;
                    if (this.opacity <= 0.1 || this.opacity >= 0.7) {
                        this.fadeDirection *= -1;
                    }
                }

                draw() {
                    const gradient = ctx.createLinearGradient(this.x1, this.y1, this.x2, this.y2);
                    const colors = this.gradientColors[this.colorIndex];
                    gradient.addColorStop(0, colors[0].replace(/[\d\.]+\)$/g, this.opacity + ')'));
                    gradient.addColorStop(1, colors[1].replace(/[\d\.]+\)$/g, this.opacity * 0.5 + ')'));

                    ctx.strokeStyle = gradient;
                    ctx.lineWidth = this.lineWidth;
                    ctx.lineCap = 'round';
                    ctx.beginPath();
                    ctx.moveTo(this.x1, this.y1);
                    ctx.lineTo(this.x2, this.y2);
                    ctx.stroke();
                }
            }

            function init() {
                linesArray = [];
                const numberOfLines = Math.floor((linesCanvas.width * linesCanvas.height) / 25000);
                for (let i = 0; i < numberOfLines; i++) {
                    linesArray.push(new AnimatedLine());
                }
            }

            function animate() {
                animationId = requestAnimationFrame(animate);
                ctx.clearRect(0, 0, linesCanvas.width, linesCanvas.height);

                for (let i = 0; i < linesArray.length; i++) {
                    linesArray[i].update();
                    linesArray[i].draw();
                }
            }

            function handleResize() {
                setCanvasSize();
                init();
            }

            window.addEventListener('resize', handleResize);
            setCanvasSize();
            init();
            animate();

            // Cleanup function
            window.addEventListener('beforeunload', () => {
                if (animationId) {
                    cancelAnimationFrame(animationId);
                }
            });
        }



        // Location slider functionality
        const locationSlider = document.getElementById('location-slider');
        const locationDots = document.querySelectorAll('.location-dot');
        let currentLocationSlide = 0;
        const totalLocationSlides = 2;

        function updateLocationSlider() {
            if (locationSlider) {
                locationSlider.style.transform = `translateX(-${currentLocationSlide * 100}%)`;

                locationDots.forEach((dot, index) => {
                    if (index === currentLocationSlide) {
                        dot.classList.remove('bg-gray-400', 'opacity-50');
                        dot.classList.add('bg-sky-400', 'opacity-100');
                    } else {
                        dot.classList.remove('bg-sky-400', 'opacity-100');
                        dot.classList.add('bg-gray-400', 'opacity-50');
                    }
                });
            }
        }

        locationDots.forEach((dot, index) => {
            dot.addEventListener('click', () => {
                currentLocationSlide = index;
                updateLocationSlider();
            });
        });

        // Auto-advance location slider
        setInterval(() => {
            currentLocationSlide = (currentLocationSlide + 1) % totalLocationSlides;
            updateLocationSlider();
        }, 4000);

        lucide.createIcons();

        // Smooth scrolling for anchor links
        document.querySelectorAll('a[href^="#"]').forEach(anchor => {
            anchor.addEventListener('click', function (e) {
                e.preventDefault();
                const target = document.querySelector(this.getAttribute('href'));
                if (target) {
                    target.scrollIntoView({
                        behavior: 'smooth',
                        block: 'start'
                    });
                }
            });
        });
    </script>
</body>
</html>