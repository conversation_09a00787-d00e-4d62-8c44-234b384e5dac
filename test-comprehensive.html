<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Comprehensive Test - X-ZoneServers</title>
    
    <!-- Comprehensive CSP for testing -->
    <meta http-equiv="Content-Security-Policy" content="default-src 'self'; script-src 'self' 'unsafe-inline' 'unsafe-eval' https://cdnjs.cloudflare.com https://unpkg.com https://cdn.jsdelivr.net; style-src 'self' 'unsafe-inline' https://cdnjs.cloudflare.com; font-src 'self' https://fonts.gstatic.com https://cdnjs.cloudflare.com; img-src 'self' data: https:; connect-src 'self'; frame-src 'none'; object-src 'none';">
    
    <!-- Load Tailwind CSS -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/tailwindcss/3.4.17/tailwind.min.css">
    
    <!-- Load Lucide icons with fallback -->
    <script src="https://unpkg.com/lucide@latest/dist/umd/lucide.js" onerror="loadLucideFallback()"></script>
    <script>
        function loadLucideFallback() {
            console.log('Loading Lucide fallback...');
            const script = document.createElement('script');
            script.src = 'https://cdn.jsdelivr.net/npm/lucide@latest/dist/umd/lucide.js';
            script.onload = function() {
                console.log('Lucide fallback loaded');
                if (typeof lucide !== 'undefined') {
                    lucide.createIcons();
                }
            };
            document.head.appendChild(script);
        }
    </script>

    <!-- Icon fallback system -->
    <script src="js/icon-fallback.js"></script>
</head>
<body class="bg-slate-950 text-white min-h-screen">
    <div class="container mx-auto p-8">
        <h1 class="text-4xl font-bold mb-8 text-center">Comprehensive Fix Test</h1>
        
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 mb-8">
            <!-- Tailwind Test -->
            <div class="bg-gradient-to-br from-blue-500/10 to-cyan-600/10 backdrop-blur-sm border border-blue-500/20 rounded-2xl p-6">
                <h2 class="text-xl font-semibold mb-4 flex items-center">
                    <i data-lucide="check-circle" class="w-5 h-5 mr-2 text-green-400"></i>
                    Tailwind CSS
                </h2>
                <p class="text-gray-300">Gradient backgrounds, borders, and responsive design working.</p>
                <div class="mt-4 bg-blue-500 text-white px-4 py-2 rounded-lg text-center">
                    Styled Button
                </div>
            </div>
            
            <!-- Lucide Icons Test -->
            <div class="bg-gradient-to-br from-purple-500/10 to-pink-600/10 backdrop-blur-sm border border-purple-500/20 rounded-2xl p-6">
                <h2 class="text-xl font-semibold mb-4 flex items-center">
                    <i data-lucide="zap" class="w-5 h-5 mr-2 text-purple-400"></i>
                    Lucide Icons
                </h2>
                <p class="text-gray-300">Icons should be visible throughout.</p>
                <div class="flex gap-2 mt-4">
                    <i data-lucide="server" class="w-6 h-6 text-blue-400"></i>
                    <i data-lucide="shield-check" class="w-6 h-6 text-green-400"></i>
                    <i data-lucide="globe" class="w-6 h-6 text-cyan-400"></i>
                    <i data-lucide="headphones" class="w-6 h-6 text-orange-400"></i>
                </div>
            </div>
            
            <!-- JavaScript Test -->
            <div class="bg-gradient-to-br from-emerald-500/10 to-teal-600/10 backdrop-blur-sm border border-emerald-500/20 rounded-2xl p-6">
                <h2 class="text-xl font-semibold mb-4 flex items-center">
                    <i data-lucide="code" class="w-5 h-5 mr-2 text-emerald-400"></i>
                    JavaScript
                </h2>
                <button id="js-test-btn" class="bg-emerald-500 hover:bg-emerald-600 px-4 py-2 rounded mb-2 w-full">
                    Test JavaScript
                </button>
                <div id="js-result" class="text-sm text-gray-400"></div>
            </div>
        </div>
        
        <!-- Console Test Results -->
        <div class="bg-slate-800 rounded-2xl p-6 mb-8">
            <h2 class="text-2xl font-semibold mb-4 flex items-center">
                <i data-lucide="terminal" class="w-6 h-6 mr-2 text-yellow-400"></i>
                Console Test Results
            </h2>
            <div id="console-output" class="bg-slate-900 rounded-lg p-4 font-mono text-sm text-green-400 min-h-[200px] overflow-y-auto">
                <div>🔍 Running comprehensive tests...</div>
            </div>
        </div>
        
        <!-- Error Summary -->
        <div class="bg-red-900/20 border border-red-500/20 rounded-2xl p-6">
            <h2 class="text-2xl font-semibold mb-4 flex items-center text-red-400">
                <i data-lucide="alert-triangle" class="w-6 h-6 mr-2"></i>
                Error Summary
            </h2>
            <div id="error-summary" class="text-gray-300">
                <div>✅ No errors detected yet...</div>
            </div>
        </div>
    </div>

    <script>
        // Comprehensive testing script
        const consoleOutput = document.getElementById('console-output');
        const errorSummary = document.getElementById('error-summary');
        const errors = [];
        
        function log(message, type = 'info') {
            const timestamp = new Date().toLocaleTimeString();
            const color = type === 'error' ? 'text-red-400' : type === 'success' ? 'text-green-400' : 'text-blue-400';
            consoleOutput.innerHTML += `<div class="${color}">[${timestamp}] ${message}</div>`;
            consoleOutput.scrollTop = consoleOutput.scrollHeight;
        }
        
        function addError(error) {
            errors.push(error);
            updateErrorSummary();
        }
        
        function updateErrorSummary() {
            if (errors.length === 0) {
                errorSummary.innerHTML = '<div class="text-green-400">✅ No errors detected!</div>';
            } else {
                errorSummary.innerHTML = errors.map(error => 
                    `<div class="text-red-400">❌ ${error}</div>`
                ).join('');
            }
        }
        
        // Override console methods to capture errors
        const originalError = console.error;
        console.error = function(...args) {
            addError(args.join(' '));
            log(`ERROR: ${args.join(' ')}`, 'error');
            originalError.apply(console, args);
        };
        
        // Test Lucide icons
        function testLucideIcons() {
            log('Testing Lucide icons...');
            if (typeof lucide !== 'undefined') {
                try {
                    lucide.createIcons();
                    log('✅ Lucide icons initialized successfully', 'success');
                } catch (error) {
                    addError(`Lucide initialization failed: ${error.message}`);
                    log(`❌ Lucide error: ${error.message}`, 'error');
                }
            } else {
                addError('Lucide library not loaded');
                log('❌ Lucide library not available', 'error');
            }
        }
        
        // Test JavaScript functionality
        document.getElementById('js-test-btn').addEventListener('click', function() {
            const result = document.getElementById('js-result');
            result.textContent = '✅ JavaScript is working!';
            result.className = 'text-sm text-green-400';
            log('✅ JavaScript click test passed', 'success');
        });
        
        // Test CSP compliance
        function testCSP() {
            log('Testing CSP compliance...');
            
            // Test if external resources loaded
            const tailwindLoaded = document.querySelector('link[href*="tailwindcss"]');
            const lucideLoaded = typeof lucide !== 'undefined';
            
            if (tailwindLoaded) {
                log('✅ Tailwind CSS link found', 'success');
            } else {
                addError('Tailwind CSS not loaded');
                log('❌ Tailwind CSS not found', 'error');
            }
            
            if (lucideLoaded) {
                log('✅ Lucide library loaded', 'success');
            } else {
                addError('Lucide library not loaded');
                log('❌ Lucide library not loaded', 'error');
            }
        }
        
        // Run tests when page loads
        document.addEventListener('DOMContentLoaded', function() {
            log('🚀 Starting comprehensive tests...');
            
            setTimeout(() => {
                testCSP();
                testLucideIcons();
                
                // Final summary
                setTimeout(() => {
                    log('🏁 Testing complete!', 'success');
                    updateErrorSummary();
                }, 1000);
            }, 500);
        });
        
        // Catch unhandled errors
        window.addEventListener('error', function(event) {
            addError(`Unhandled error: ${event.message} at ${event.filename}:${event.lineno}`);
            log(`❌ Unhandled error: ${event.message}`, 'error');
        });
        
        window.addEventListener('unhandledrejection', function(event) {
            addError(`Unhandled promise rejection: ${event.reason}`);
            log(`❌ Promise rejection: ${event.reason}`, 'error');
        });
    </script>
</body>
</html>
