# X-ZoneServers Website

## Component System Implementation

The website now uses a modular component system for both header and footer for better maintainability.

### Files Structure

- `header-loader.js` - JavaScript to load the header into each page
- `footer-loader.js` - JavaScript to load the footer into each page
- `index.html` - Main homepage (updated to use header & footer components)
- `vps.html` - VPS plans page (updated to use header & footer components)
- `dedicated.html` - Dedicated servers page (updated to use header & footer components)

### How It Works

#### Header Component
1. Each HTML page includes `header-loader.js` in the head section
2. Each page has a `<div id="header-placeholder"></div>` where the header will be loaded
3. The header-loader script injects the header HTML and initializes functionality:
   - Mobile menu toggle functionality
   - Active navigation highlighting based on current page
   - Dynamic CTA button text/links based on current page

#### Footer Component
1. Each HTML page includes `footer-loader.js` in the head section
2. Each page has a `<div id="footer-placeholder"></div>` where the footer will be loaded
3. The footer-loader script injects the footer HTML with consistent styling and links

### Benefits

- **Single Source of Truth**: Header and footer content maintained in component files
- **Consistent Layout**: All pages automatically get the same header/footer structure
- **Easy Updates**: Changes to components only need to be made in the loader files
- **Smart Active States**: Navigation automatically highlights the current page
- **Dynamic CTAs**: Call-to-action buttons adapt based on the current page
- **Reduced Code Duplication**: No need to copy/paste header/footer across pages

### Page-Specific Behavior

- **Index Page**: CTA button says "Get Started" and links to #streaming-vps
- **VPS Page**: CTA button says "Get Started" and links to #vps-plans
- **Dedicated Page**: CTA button says "Configure Server" and links to #server-configurator

### Navigation Links

The header includes navigation to:
- Streaming VPS (vps.html)
- Dedicated Streaming (dedicated.html)
- Features (index.html#features)
- FAQ (index.html#faq)

All navigation links are responsive and work on both desktop and mobile devices.
