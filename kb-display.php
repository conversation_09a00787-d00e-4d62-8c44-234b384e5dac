<?php
/**
 * SEO-Optimized Knowledge Base Display System
 * Dynamic knowledge base frontend with search functionality
 */

require_once 'whmcs-kb-module.php';

// Initialize KB module
$kbModule = init_whmcs_kb_module();

// Get URL parameters
$requestUri = $_SERVER['REQUEST_URI'];
$urlPath = parse_url($requestUri, PHP_URL_PATH);
$pathSegments = explode('/', trim($urlPath, '/'));

// Determine view type
$viewType = 'index'; // default to KB index
$articleSlug = null;
$categorySlug = null;
$searchQuery = null;
$page = 1;

if (count($pathSegments) >= 2 && $pathSegments[0] === 'kb') {
    if (!empty($pathSegments[1])) {
        // Check if it's search, category, or article
        if ($pathSegments[1] === 'search') {
            $viewType = 'search';
            $searchQuery = $_GET['q'] ?? '';
        } elseif ($pathSegments[1] === 'category' && !empty($pathSegments[2])) {
            $viewType = 'category';
            $categorySlug = $pathSegments[2];
        } else {
            $viewType = 'article';
            $articleSlug = $pathSegments[1];
        }
    }
}

// Handle search from form submission
if (isset($_GET['search']) && !empty($_GET['q'])) {
    $viewType = 'search';
    $searchQuery = trim($_GET['q']);
}

// Get data based on view type
$pageData = [];
$articles = [];
$categories = [];
$currentArticle = null;
$searchResults = [];

// Get categories for navigation
$categoriesResult = $kbModule->getCategories(['status' => 'active']);
$categories = $categoriesResult['success'] ? $categoriesResult['categories'] : [];

switch ($viewType) {
    case 'article':
        $result = $kbModule->getArticleBySlug($articleSlug);
        if ($result['success']) {
            $currentArticle = $result['article'];
            $pageData = [
                'title' => $currentArticle->meta_title ?: $currentArticle->title,
                'description' => $currentArticle->meta_description ?: $currentArticle->excerpt,
                'keywords' => $currentArticle->keywords,
                'canonical' => "https://x-zoneservers.com/kb/{$currentArticle->slug}/",
                'type' => 'article'
            ];
        } else {
            header('HTTP/1.0 404 Not Found');
            $viewType = '404';
        }
        break;
        
    case 'category':
        $result = $kbModule->getArticles([
            'status' => 'published',
            'category_slug' => $categorySlug,
            'limit' => 20
        ]);
        
        if ($result['success'] && !empty($result['articles'])) {
            $articles = $result['articles'];
            $categoryName = $articles[0]->category_name;
            $pageData = [
                'title' => "{$categoryName} - Knowledge Base | X-ZoneServers",
                'description' => "Find helpful guides and tutorials about {$categoryName} from X-ZoneServers support team.",
                'canonical' => "https://x-zoneservers.com/kb/category/{$categorySlug}/",
                'type' => 'website'
            ];
        } else {
            header('HTTP/1.0 404 Not Found');
            $viewType = '404';
        }
        break;
        
    case 'search':
        if (!empty($searchQuery)) {
            $result = $kbModule->getArticles([
                'status' => 'published',
                'search' => $searchQuery,
                'limit' => 50
            ]);
            
            if ($result['success']) {
                $searchResults = $result['articles'];
                // Record search query for analytics
                $kbModule->recordSearchQuery($searchQuery, count($searchResults), $_SERVER['REMOTE_ADDR']);
            }
        }
        
        $pageData = [
            'title' => $searchQuery ? "Search: {$searchQuery} - Knowledge Base | X-ZoneServers" : "Search - Knowledge Base | X-ZoneServers",
            'description' => "Search our knowledge base for helpful guides and solutions.",
            'canonical' => "https://x-zoneservers.com/kb/search/",
            'type' => 'website'
        ];
        break;
        
    default: // index
        $featuredResult = $kbModule->getArticles([
            'status' => 'published',
            'featured' => true,
            'limit' => 6
        ]);
        
        $recentResult = $kbModule->getArticles([
            'status' => 'published',
            'limit' => 8
        ]);
        
        $featuredArticles = $featuredResult['success'] ? $featuredResult['articles'] : [];
        $recentArticles = $recentResult['success'] ? $recentResult['articles'] : [];
        
        $pageData = [
            'title' => 'Knowledge Base - X-ZoneServers Support Center',
            'description' => 'Find answers to your questions with our comprehensive knowledge base. Server management, troubleshooting guides, and technical documentation.',
            'canonical' => 'https://x-zoneservers.com/kb/',
            'type' => 'website'
        ];
        break;
}

// Helper functions
function formatKbDate($date) {
    return date('M j, Y', strtotime($date));
}

function getDifficultyBadge($difficulty) {
    $classes = [
        'beginner' => 'bg-green-100 text-green-800',
        'intermediate' => 'bg-yellow-100 text-yellow-800',
        'advanced' => 'bg-red-100 text-red-800'
    ];
    
    return $classes[$difficulty] ?? $classes['beginner'];
}

function getCategoryIcon($icon) {
    $icons = [
        'rocket' => 'M13 10V3L4 14h7v7l9-11h-7z',
        'server' => 'M5 12h14M5 12a2 2 0 01-2-2V6a2 2 0 012-2h14a2 2 0 012 2v4a2 2 0 01-2 2M5 12a2 2 0 00-2 2v4a2 2 0 002 2h14a2 2 0 002-2v-4a2 2 0 00-2-2m-2-4h.01M17 16h.01',
        'settings' => 'M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z M15 12a3 3 0 11-6 0 3 3 0 016 0z',
        'tool' => 'M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z M15 12a3 3 0 11-6 0 3 3 0 016 0z',
        'shield' => 'M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z',
        'zap' => 'M13 10V3L4 14h7v7l9-11h-7z',
        'credit-card' => 'M3 10h18M7 15h1m4 0h1m-7 4h12a3 3 0 003-3V8a3 3 0 00-3-3H6a3 3 0 00-3 3v8a3 3 0 003 3z',
        'folder' => 'M3 7v10a2 2 0 002 2h14a2 2 0 002-2V9a2 2 0 00-2-2H5a2 2 0 00-2-2z'
    ];
    
    return $icons[$icon] ?? $icons['folder'];
}
?>

<!DOCTYPE html>
<html lang="en" class="scroll-smooth">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo htmlspecialchars($pageData['title']); ?></title>
    <meta name="description" content="<?php echo htmlspecialchars($pageData['description']); ?>">
    <?php if (!empty($pageData['keywords'])): ?>
    <meta name="keywords" content="<?php echo htmlspecialchars($pageData['keywords']); ?>">
    <?php endif; ?>
    <meta name="author" content="X-ZoneServers">
    <meta name="robots" content="index, follow">
    <link rel="canonical" href="<?php echo htmlspecialchars($pageData['canonical']); ?>">

    <!-- Open Graph -->
    <meta property="og:type" content="<?php echo $pageData['type']; ?>">
    <meta property="og:url" content="<?php echo htmlspecialchars($pageData['canonical']); ?>">
    <meta property="og:title" content="<?php echo htmlspecialchars($pageData['title']); ?>">
    <meta property="og:description" content="<?php echo htmlspecialchars($pageData['description']); ?>">
    <meta property="og:site_name" content="X-ZoneServers">
    
    <?php if ($viewType === 'article' && $currentArticle): ?>
    <meta property="article:published_time" content="<?php echo date('c', strtotime($currentArticle->published_at)); ?>">
    <meta property="article:modified_time" content="<?php echo date('c', strtotime($currentArticle->updated_at)); ?>">
    <meta property="article:author" content="<?php echo htmlspecialchars($currentArticle->author_name); ?>">
    <meta property="article:section" content="<?php echo htmlspecialchars($currentArticle->category_name); ?>">
    <?php if (!empty($currentArticle->tags)): ?>
        <?php foreach ($currentArticle->tags as $tag): ?>
    <meta property="article:tag" content="<?php echo htmlspecialchars($tag); ?>">
        <?php endforeach; ?>
    <?php endif; ?>
    <?php endif; ?>

    <!-- Twitter -->
    <meta name="twitter:card" content="summary_large_image">
    <meta name="twitter:title" content="<?php echo htmlspecialchars($pageData['title']); ?>">
    <meta name="twitter:description" content="<?php echo htmlspecialchars($pageData['description']); ?>">
    
    <!-- Structured Data -->
    <?php if ($viewType === 'article' && $currentArticle): ?>
    <script type="application/ld+json">
    {
        "@context": "https://schema.org",
        "@type": "Article",
        "@id": "<?php echo htmlspecialchars($pageData['canonical']); ?>",
        "headline": "<?php echo htmlspecialchars($currentArticle->title); ?>",
        "description": "<?php echo htmlspecialchars($currentArticle->excerpt); ?>",
        "datePublished": "<?php echo date('c', strtotime($currentArticle->published_at)); ?>",
        "dateModified": "<?php echo date('c', strtotime($currentArticle->updated_at)); ?>",
        "author": {
            "@type": "Person",
            "name": "<?php echo htmlspecialchars($currentArticle->author_name); ?>"
        },
        "publisher": {
            "@type": "Organization",
            "name": "X-ZoneServers",
            "logo": {
                "@type": "ImageObject",
                "url": "https://x-zoneservers.com/images/logo.png"
            }
        },
        "mainEntityOfPage": {
            "@type": "WebPage",
            "@id": "<?php echo htmlspecialchars($pageData['canonical']); ?>"
        },
        "articleSection": "<?php echo htmlspecialchars($currentArticle->category_name); ?>",
        "keywords": [<?php echo !empty($currentArticle->tags) ? '"' . implode('", "', $currentArticle->tags) . '"' : ''; ?>]
    }
    </script>
    
    <!-- FAQ Schema for Q&A articles -->
    <script type="application/ld+json">
    {
        "@context": "https://schema.org",
        "@type": "FAQPage",
        "mainEntity": {
            "@type": "Question",
            "name": "<?php echo htmlspecialchars($currentArticle->title); ?>",
            "acceptedAnswer": {
                "@type": "Answer",
                "text": "<?php echo htmlspecialchars(strip_tags($currentArticle->content)); ?>"
            }
        }
    }
    </script>
    <?php endif; ?>

    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="style.css">
    
    <style>
        .kb-search-highlight { background-color: #fef3c7; }
        .helpfulness-bar { background: linear-gradient(90deg, #10b981 var(--helpful-percent, 0%), #e5e7eb var(--helpful-percent, 0%)); }
    </style>
</head>
<body class="bg-slate-950 text-white">
    <!-- Header -->
    <div id="header-placeholder"></div>

    <?php if ($viewType === 'article' && $currentArticle): ?>
    <!-- Single Article View -->
    <article class="py-16">
        <div class="container mx-auto px-6">
            <!-- Article Header -->
            <header class="max-w-4xl mx-auto text-center mb-12">
                <!-- Breadcrumbs -->
                <nav class="mb-8" aria-label="Breadcrumb">
                    <ol class="flex items-center justify-center space-x-2 text-sm" itemscope itemtype="https://schema.org/BreadcrumbList">
                        <li itemprop="itemListElement" itemscope itemtype="https://schema.org/ListItem">
                            <a href="/" class="text-blue-400 hover:text-blue-300" itemprop="item">
                                <span itemprop="name">Home</span>
                            </a>
                            <meta itemprop="position" content="1" />
                        </li>
                        <li class="text-gray-400 mx-2">/</li>
                        <li itemprop="itemListElement" itemscope itemtype="https://schema.org/ListItem">
                            <a href="/kb/" class="text-blue-400 hover:text-blue-300" itemprop="item">
                                <span itemprop="name">Knowledge Base</span>
                            </a>
                            <meta itemprop="position" content="2" />
                        </li>
                        <li class="text-gray-400 mx-2">/</li>
                        <li itemprop="itemListElement" itemscope itemtype="https://schema.org/ListItem">
                            <a href="/kb/category/<?php echo htmlspecialchars($currentArticle->category_slug); ?>/" class="text-blue-400 hover:text-blue-300" itemprop="item">
                                <span itemprop="name"><?php echo htmlspecialchars($currentArticle->category_name); ?></span>
                            </a>
                            <meta itemprop="position" content="3" />
                        </li>
                        <li class="text-gray-400 mx-2">/</li>
                        <li itemprop="itemListElement" itemscope itemtype="https://schema.org/ListItem">
                            <span itemprop="name" class="text-white"><?php echo htmlspecialchars($currentArticle->title); ?></span>
                            <meta itemprop="position" content="4" />
                        </li>
                    </ol>
                </nav>

                <!-- Category Badge and Difficulty -->
                <div class="flex items-center justify-center gap-3 mb-6">
                    <a href="/kb/category/<?php echo htmlspecialchars($currentArticle->category_slug); ?>/" 
                       class="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium text-white hover:opacity-80"
                       style="background-color: <?php echo $currentArticle->category_color; ?>">
                        <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="<?php echo getCategoryIcon($currentArticle->category_icon); ?>"></path>
                        </svg>
                        <?php echo htmlspecialchars($currentArticle->category_name); ?>
                    </a>
                    <span class="px-3 py-1 rounded-full text-sm font-medium <?php echo getDifficultyBadge($currentArticle->difficulty); ?>">
                        <?php echo ucfirst($currentArticle->difficulty); ?>
                    </span>
                </div>

                <h1 class="text-4xl md:text-5xl font-bold mb-6 bg-gradient-to-r from-white via-blue-100 to-blue-200 bg-clip-text text-transparent">
                    <?php echo htmlspecialchars($currentArticle->title); ?>
                </h1>

                <p class="text-xl text-gray-300 mb-8 leading-relaxed">
                    <?php echo htmlspecialchars($currentArticle->excerpt); ?>
                </p>

                <!-- Article Meta -->
                <div class="flex flex-wrap items-center justify-center gap-6 text-sm text-gray-400">
                    <div class="flex items-center gap-2">
                        <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"></path>
                        </svg>
                        <span><?php echo htmlspecialchars($currentArticle->author_name); ?></span>
                    </div>
                    <div class="flex items-center gap-2">
                        <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"></path>
                        </svg>
                        <?php echo formatKbDate($currentArticle->published_at); ?>
                    </div>
                    <div class="flex items-center gap-2">
                        <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                        </svg>
                        <?php echo $currentArticle->estimated_read_time; ?> min read
                    </div>
                    <div class="flex items-center gap-2">
                        <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"></path>
                        </svg>
                        <?php echo number_format($currentArticle->view_count); ?> views
                    </div>
                </div>
            </header>

            <!-- Article Content -->
            <div class="max-w-4xl mx-auto">
                <div class="prose prose-lg prose-invert max-w-none">
                    <?php echo $currentArticle->content; ?>
                </div>

                <!-- Helpfulness Section -->
                <div class="mt-12 pt-8 border-t border-slate-800">
                    <div class="text-center">
                        <h3 class="text-lg font-semibold mb-4">Was this article helpful?</h3>
                        <div class="flex items-center justify-center gap-4 mb-4">
                            <button onclick="voteOnArticle(<?php echo $currentArticle->id; ?>, 'helpful')" 
                                    class="flex items-center gap-2 px-4 py-2 bg-green-600 hover:bg-green-700 text-white rounded-md transition-colors">
                                <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M14 10h4.764a2 2 0 011.789 2.894l-3.5 7A2 2 0 0115.263 21h-4.017c-.163 0-.326-.02-.485-.06L7 20m7-10V5a2 2 0 00-2-2h-.095c-.5 0-.905.405-.905.905 0 .714-.211 1.412-.608 2.006L9 7m5 3v3.5M9 7l-3.325 1.663a.5.5 0 01-.65-.62L7 3"></path>
                                </svg>
                                Yes (<?php echo $currentArticle->helpful_votes; ?>)
                            </button>
                            <button onclick="voteOnArticle(<?php echo $currentArticle->id; ?>, 'not_helpful')"
                                    class="flex items-center gap-2 px-4 py-2 bg-red-600 hover:bg-red-700 text-white rounded-md transition-colors">
                                <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 14H5.236a2 2 0 01-1.789-2.894l3.5-7A2 2 0 018.736 3h4.018c.163 0 .326.02.485.06L17 4m-7 10v2a2 2 0 002 2h.095c.5 0 .905-.405.905-.905 0-.714.211-1.412.608-2.006L15 17m-7-10V5.5M15 17l3.326-1.663a.5.5 0 01.65.62L17 21"></path>
                                </svg>
                                No (<?php echo $currentArticle->not_helpful_votes; ?>)
                            </button>
                        </div>
                        <?php if ($currentArticle->helpful_votes + $currentArticle->not_helpful_votes > 0): ?>
                        <div class="text-sm text-gray-400">
                            <?php echo $currentArticle->helpfulness_ratio; ?>% of readers found this helpful
                        </div>
                        <?php endif; ?>
                    </div>
                </div>

                <!-- Tags -->
                <?php if (!empty($currentArticle->tags)): ?>
                <div class="mt-8 pt-8 border-t border-slate-800">
                    <div class="flex flex-wrap gap-2">
                        <?php foreach ($currentArticle->tags as $tag): ?>
                        <span class="px-3 py-1 bg-slate-800 text-gray-300 rounded-full text-sm">
                            <?php echo htmlspecialchars($tag); ?>
                        </span>
                        <?php endforeach; ?>
                    </div>
                </div>
                <?php endif; ?>
            </div>
        </div>
    </article>

    <?php elseif ($viewType === '404'): ?>
    <!-- 404 Error -->
    <div class="py-32 text-center">
        <h1 class="text-6xl font-bold text-gray-600 mb-4">404</h1>
        <h2 class="text-2xl font-semibold mb-4">Article Not Found</h2>
        <p class="text-gray-400 mb-8">The knowledge base article you're looking for doesn't exist or has been moved.</p>
        <a href="/kb/" class="bg-blue-600 hover:bg-blue-700 text-white px-6 py-3 rounded-lg">
            Back to Knowledge Base
        </a>
    </div>

    <?php elseif ($viewType === 'search'): ?>
    <!-- Search Results -->
    <section class="py-16">
        <div class="container mx-auto px-6">
            <div class="max-w-4xl mx-auto">
                <!-- Search Header -->
                <div class="text-center mb-12">
                    <h1 class="text-4xl font-bold mb-4">
                        <?php if ($searchQuery): ?>
                            Search Results for "<?php echo htmlspecialchars($searchQuery); ?>"
                        <?php else: ?>
                            Search Knowledge Base
                        <?php endif; ?>
                    </h1>
                    <?php if ($searchQuery && !empty($searchResults)): ?>
                    <p class="text-gray-400">Found <?php echo count($searchResults); ?> result<?php echo count($searchResults) !== 1 ? 's' : ''; ?></p>
                    <?php endif; ?>
                </div>

                <!-- Search Form -->
                <form method="GET" action="/kb/search/" class="mb-12">
                    <div class="flex gap-4 max-w-2xl mx-auto">
                        <input type="text" name="q" value="<?php echo htmlspecialchars($searchQuery); ?>" 
                               placeholder="Search for help articles..."
                               class="flex-1 px-4 py-3 bg-slate-800 border border-slate-700 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                        <button type="submit" class="px-6 py-3 bg-blue-600 hover:bg-blue-700 text-white rounded-lg font-medium">
                            Search
                        </button>
                    </div>
                </form>

                <!-- Search Results -->
                <?php if ($searchQuery): ?>
                    <?php if (empty($searchResults)): ?>
                    <div class="text-center py-16">
                        <svg class="mx-auto h-12 w-12 text-gray-400 mb-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
                        </svg>
                        <h3 class="text-xl font-medium text-gray-300 mb-2">No results found</h3>
                        <p class="text-gray-500 mb-6">Try different keywords or browse our categories below.</p>
                        <a href="/kb/" class="bg-blue-600 hover:bg-blue-700 text-white px-6 py-3 rounded-lg">
                            Browse Categories
                        </a>
                    </div>
                    <?php else: ?>
                    <div class="space-y-6">
                        <?php foreach ($searchResults as $article): ?>
                        <div class="bg-slate-800 rounded-xl p-6 hover:bg-slate-750 transition-colors">
                            <div class="flex items-start justify-between">
                                <div class="flex-1">
                                    <div class="flex items-center gap-3 mb-2">
                                        <h2 class="text-xl font-semibold text-white hover:text-blue-400 transition-colors">
                                            <a href="/kb/<?php echo htmlspecialchars($article->slug); ?>/">
                                                <?php echo htmlspecialchars($article->title); ?>
                                            </a>
                                        </h2>
                                        <span class="px-2 py-1 rounded-full text-xs font-medium <?php echo getDifficultyBadge($article->difficulty); ?>">
                                            <?php echo ucfirst($article->difficulty); ?>
                                        </span>
                                    </div>
                                    
                                    <p class="text-gray-400 mb-4">
                                        <?php echo htmlspecialchars($article->excerpt); ?>
                                    </p>
                                    
                                    <div class="flex items-center gap-4 text-sm text-gray-500">
                                        <span class="flex items-center gap-1">
                                            <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="<?php echo getCategoryIcon($article->category_icon); ?>"></path>
                                            </svg>
                                            <?php echo htmlspecialchars($article->category_name); ?>
                                        </span>
                                        <span class="flex items-center gap-1">
                                            <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"></path>
                                            </svg>
                                            <?php echo number_format($article->view_count); ?> views
                                        </span>
                                        <span class="flex items-center gap-1">
                                            <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                            </svg>
                                            <?php echo $article->estimated_read_time; ?> min read
                                        </span>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <?php endforeach; ?>
                    </div>
                    <?php endif; ?>
                <?php endif; ?>
            </div>
        </div>
    </section>

    <?php elseif ($viewType === 'category'): ?>
    <!-- Category View -->
    <section class="py-16">
        <div class="container mx-auto px-6">
            <div class="text-center mb-12">
                <h1 class="text-4xl font-bold mb-4"><?php echo htmlspecialchars($categoryName); ?></h1>
                <p class="text-gray-400 max-w-2xl mx-auto">
                    Find helpful guides and tutorials about <?php echo htmlspecialchars($categoryName); ?>
                </p>
            </div>

            <?php if (empty($articles)): ?>
            <div class="text-center py-16">
                <svg class="mx-auto h-12 w-12 text-gray-400 mb-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                </svg>
                <h3 class="text-xl font-medium text-gray-300 mb-2">No articles found</h3>
                <p class="text-gray-500">Check back soon for new content in this category!</p>
            </div>
            <?php else: ?>
            <div class="grid md:grid-cols-2 gap-8">
                <?php foreach ($articles as $article): ?>
                <article class="bg-slate-800 rounded-xl overflow-hidden hover:bg-slate-750 transition-colors group">
                    <div class="p-6">
                        <div class="flex items-center gap-3 mb-3">
                            <h2 class="text-xl font-bold group-hover:text-blue-400 transition-colors">
                                <a href="/kb/<?php echo htmlspecialchars($article->slug); ?>/">
                                    <?php echo htmlspecialchars($article->title); ?>
                                </a>
                            </h2>
                            <span class="px-2 py-1 rounded-full text-xs font-medium <?php echo getDifficultyBadge($article->difficulty); ?>">
                                <?php echo ucfirst($article->difficulty); ?>
                            </span>
                        </div>
                        
                        <p class="text-gray-400 mb-4 line-clamp-3">
                            <?php echo htmlspecialchars($article->excerpt); ?>
                        </p>
                        
                        <div class="flex items-center justify-between">
                            <div class="flex items-center gap-4 text-sm text-gray-500">
                                <span class="flex items-center gap-1">
                                    <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"></path>
                                    </svg>
                                    <?php echo number_format($article->view_count); ?>
                                </span>
                                <span class="flex items-center gap-1">
                                    <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                    </svg>
                                    <?php echo $article->estimated_read_time; ?> min
                                </span>
                            </div>
                        </div>
                    </div>
                </article>
                <?php endforeach; ?>
            </div>
            <?php endif; ?>
        </div>
    </section>

    <?php else: ?>
    <!-- Knowledge Base Index -->
    <!-- Hero Section -->
    <section class="relative py-20 bg-gradient-to-br from-slate-900 via-blue-900 to-slate-900">
        <div class="absolute inset-0 bg-[url('data:image/svg+xml,%3Csvg width="60" height="60" viewBox="0 0 60 60" xmlns="http://www.w3.org/2000/svg"%3E%3Cg fill="none" fill-rule="evenodd"%3E%3Cg fill="%23ffffff" fill-opacity="0.05"%3E%3Ccircle cx="30" cy="30" r="2"/%3E%3C/g%3E%3C/g%3E%3C/svg%3E')] opacity-20"></div>
        
        <div class="container mx-auto px-6 relative">
            <div class="text-center max-w-4xl mx-auto">
                <h1 class="text-5xl md:text-6xl font-bold mb-6 bg-gradient-to-r from-white via-blue-100 to-blue-200 bg-clip-text text-transparent">
                    Knowledge Base
                </h1>
                <p class="text-xl text-gray-300 mb-12 leading-relaxed">
                    Find answers to your questions with our comprehensive support documentation. 
                    Server management guides, troubleshooting tips, and technical resources.
                </p>
                
                <!-- Search Form -->
                <form method="GET" action="/kb/search/" class="max-w-2xl mx-auto">
                    <div class="flex gap-4">
                        <input type="text" name="q" placeholder="Search our knowledge base..." 
                               class="flex-1 px-6 py-4 bg-white/10 border border-white/20 rounded-lg text-white placeholder-gray-300 focus:outline-none focus:ring-2 focus:ring-blue-400 focus:border-transparent">
                        <button type="submit" class="px-8 py-4 bg-blue-600 hover:bg-blue-700 text-white font-medium rounded-lg transition-colors">
                            Search
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </section>

    <!-- Categories Section -->
    <section class="py-16 bg-slate-900">
        <div class="container mx-auto px-6">
            <div class="text-center mb-12">
                <h2 class="text-3xl font-bold mb-4">Browse by Category</h2>
                <p class="text-gray-400 max-w-2xl mx-auto">
                    Find the help you need organized by topic and expertise level
                </p>
            </div>

            <div class="grid md:grid-cols-2 lg:grid-cols-3 gap-6">
                <?php foreach ($categories as $category): ?>
                <div class="bg-slate-800 rounded-xl p-6 hover:bg-slate-750 transition-colors group cursor-pointer">
                    <a href="/kb/category/<?php echo htmlspecialchars($category->slug); ?>/" class="block">
                        <div class="flex items-center mb-4">
                            <div class="w-12 h-12 rounded-lg flex items-center justify-center text-white mr-4" 
                                 style="background-color: <?php echo $category->color; ?>">
                                <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="<?php echo getCategoryIcon($category->icon); ?>"></path>
                                </svg>
                            </div>
                            <div class="flex-1">
                                <h3 class="text-xl font-bold mb-1 group-hover:text-blue-400 transition-colors">
                                    <?php echo htmlspecialchars($category->name); ?>
                                </h3>
                                <div class="text-blue-400 font-medium text-sm">
                                    <?php echo $category->article_count; ?> articles
                                </div>
                            </div>
                        </div>
                        <p class="text-gray-400 text-sm">
                            <?php echo htmlspecialchars($category->description); ?>
                        </p>
                    </a>
                </div>
                <?php endforeach; ?>
            </div>
        </div>
    </section>

    <!-- Featured Articles Section -->
    <?php if (!empty($featuredArticles)): ?>
    <section class="py-16 bg-slate-950">
        <div class="container mx-auto px-6">
            <div class="text-center mb-12">
                <h2 class="text-3xl font-bold mb-4">Featured Articles</h2>
                <p class="text-gray-400 max-w-2xl mx-auto">
                    Popular guides and essential knowledge for managing your hosting services
                </p>
            </div>

            <div class="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
                <?php foreach ($featuredArticles as $article): ?>
                <article class="bg-slate-800 rounded-xl overflow-hidden hover:bg-slate-750 transition-colors group">
                    <div class="p-6">
                        <div class="flex items-center gap-3 mb-3">
                            <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium text-white" 
                                  style="background-color: <?php echo $article->category_color; ?>">
                                <svg class="w-3 h-3 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="<?php echo getCategoryIcon($article->category_icon); ?>"></path>
                                </svg>
                                <?php echo htmlspecialchars($article->category_name); ?>
                            </span>
                            <span class="px-2 py-1 rounded-full text-xs font-medium <?php echo getDifficultyBadge($article->difficulty); ?>">
                                <?php echo ucfirst($article->difficulty); ?>
                            </span>
                        </div>
                        
                        <h3 class="text-xl font-bold mb-3 group-hover:text-blue-400 transition-colors">
                            <a href="/kb/<?php echo htmlspecialchars($article->slug); ?>/">
                                <?php echo htmlspecialchars($article->title); ?>
                            </a>
                        </h3>
                        
                        <p class="text-gray-400 mb-4 line-clamp-3">
                            <?php echo htmlspecialchars($article->excerpt); ?>
                        </p>
                        
                        <div class="flex items-center justify-between text-sm text-gray-500">
                            <span class="flex items-center gap-1">
                                <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"></path>
                                </svg>
                                <?php echo number_format($article->view_count); ?>
                            </span>
                            <span class="flex items-center gap-1">
                                <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                </svg>
                                <?php echo $article->estimated_read_time; ?> min
                            </span>
                        </div>
                    </div>
                </article>
                <?php endforeach; ?>
            </div>
        </div>
    </section>
    <?php endif; ?>

    <!-- Recent Articles Section -->
    <?php if (!empty($recentArticles)): ?>
    <section class="py-16 bg-slate-900">
        <div class="container mx-auto px-6">
            <div class="flex justify-between items-center mb-12">
                <div>
                    <h2 class="text-3xl font-bold mb-2">Recent Articles</h2>
                    <p class="text-gray-400">Latest updates and new content</p>
                </div>
                <a href="/kb/search/" class="text-blue-400 hover:text-blue-300 font-medium">
                    View All →
                </a>
            </div>

            <div class="grid md:grid-cols-2 gap-6">
                <?php foreach (array_slice($recentArticles, 0, 6) as $article): ?>
                <div class="flex items-start gap-4 p-4 bg-slate-800 rounded-lg hover:bg-slate-750 transition-colors group">
                    <div class="w-12 h-12 rounded-lg flex items-center justify-center text-white flex-shrink-0" 
                         style="background-color: <?php echo $article->category_color; ?>">
                        <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="<?php echo getCategoryIcon($article->category_icon); ?>"></path>
                        </svg>
                    </div>
                    <div class="flex-1 min-w-0">
                        <h3 class="font-semibold text-white mb-1 group-hover:text-blue-400 transition-colors">
                            <a href="/kb/<?php echo htmlspecialchars($article->slug); ?>/" class="block truncate">
                                <?php echo htmlspecialchars($article->title); ?>
                            </a>
                        </h3>
                        <p class="text-gray-400 text-sm mb-2 line-clamp-2">
                            <?php echo htmlspecialchars($article->excerpt); ?>
                        </p>
                        <div class="flex items-center gap-3 text-xs text-gray-500">
                            <span><?php echo htmlspecialchars($article->category_name); ?></span>
                            <span>•</span>
                            <span><?php echo $article->estimated_read_time; ?> min read</span>
                        </div>
                    </div>
                </div>
                <?php endforeach; ?>
            </div>
        </div>
    </section>
    <?php endif; ?>
    <?php endif; ?>

    <!-- Footer -->
    <div id="footer-placeholder"></div>

    <!-- Scripts -->
    <script src="js/header-loader.js"></script>
    <script src="js/footer-loader.js"></script>
    
    <script>
        // Vote on article helpfulness
        function voteOnArticle(articleId, voteType) {
            fetch('/kb-vote.php', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/x-www-form-urlencoded',
                },
                body: `article_id=${articleId}&vote_type=${voteType}`
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    alert(data.message);
                    location.reload();
                } else {
                    alert(data.message);
                }
            })
            .catch(error => {
                console.error('Error:', error);
                alert('An error occurred while submitting your vote.');
            });
        }

        // Search functionality enhancements
        document.addEventListener('DOMContentLoaded', function() {
            // Add search shortcuts
            document.addEventListener('keydown', function(e) {
                if ((e.ctrlKey || e.metaKey) && e.key === 'k') {
                    e.preventDefault();
                    const searchInput = document.querySelector('input[name="q"]');
                    if (searchInput) {
                        searchInput.focus();
                    }
                }
            });
        });
    </script>
</body>
</html>