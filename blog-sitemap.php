<?php
/**
 * Blog Sitemap Generator for SEO
 * Generates XML sitemap for all published blog posts
 */

require_once 'whmcs-blog-module.php';

// Set proper headers for XML sitemap
header('Content-Type: application/xml; charset=UTF-8');

// Initialize blog module
$blogModule = init_whmcs_blog_module();

// Get all published blog posts for sitemap
$result = $blogModule->getSitemapData();

if (!$result['success']) {
    // Return empty sitemap if there's an error
    $posts = [];
} else {
    $posts = $result['posts'];
}

// Generate XML sitemap
echo '<?xml version="1.0" encoding="UTF-8"?>';
?>
<urlset xmlns="http://www.sitemaps.org/schemas/sitemap/0.9"
        xmlns:news="http://www.google.com/schemas/sitemap-news/0.9"
        xmlns:xhtml="http://www.w3.org/1999/xhtml"
        xmlns:image="http://www.google.com/schemas/sitemap-image/1.1">
        
    <!-- Blog Index -->
    <url>
        <loc>https://x-zoneservers.com/blog/</loc>
        <lastmod><?php echo date('c'); ?></lastmod>
        <changefreq>daily</changefreq>
        <priority>0.9</priority>
    </url>
    
    <!-- Blog Categories -->
    <url>
        <loc>https://x-zoneservers.com/blog/server-administration/</loc>
        <lastmod><?php echo date('c'); ?></lastmod>
        <changefreq>weekly</changefreq>
        <priority>0.8</priority>
    </url>
    
    <url>
        <loc>https://x-zoneservers.com/blog/network-optimization/</loc>
        <lastmod><?php echo date('c'); ?></lastmod>
        <changefreq>weekly</changefreq>
        <priority>0.8</priority>
    </url>
    
    <url>
        <loc>https://x-zoneservers.com/blog/security-compliance/</loc>
        <lastmod><?php echo date('c'); ?></lastmod>
        <changefreq>weekly</changefreq>
        <priority>0.8</priority>
    </url>
    
    <url>
        <loc>https://x-zoneservers.com/blog/case-studies/</loc>
        <lastmod><?php echo date('c'); ?></lastmod>
        <changefreq>weekly</changefreq>
        <priority>0.8</priority>
    </url>
    
    <?php foreach ($posts as $post): ?>
    <!-- Blog Post: <?php echo htmlspecialchars($post->slug); ?> -->
    <url>
        <loc>https://x-zoneservers.com/blog/<?php echo htmlspecialchars($post->slug); ?>/</loc>
        <lastmod><?php echo date('c', strtotime($post->updated_at)); ?></lastmod>
        <changefreq>monthly</changefreq>
        <priority>0.7</priority>
        
        <!-- Google News Sitemap Extension (for recent posts) -->
        <?php if (strtotime($post->published_at) > strtotime('-30 days')): ?>
        <news:news>
            <news:publication>
                <news:name>X-ZoneServers Blog</news:name>
                <news:language>en</news:language>
            </news:publication>
            <news:publication_date><?php echo date('c', strtotime($post->published_at)); ?></news:publication_date>
            <news:title><?php echo htmlspecialchars($post->title); ?></news:title>
            <news:keywords><?php echo htmlspecialchars($post->keywords ?: 'hosting, servers, infrastructure'); ?></news:keywords>
        </news:news>
        <?php endif; ?>
    </url>
    <?php endforeach; ?>
    
</urlset>