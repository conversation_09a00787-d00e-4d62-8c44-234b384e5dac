<?php
/**
 * WHMCS Ticket Integration Handler
 * Processes contact form submissions and creates tickets in WHMCS
 */

// Load configuration
$config = require_once 'whmcs-config.php';

$whmcs_url = $config['whmcs_url'];
$whmcs_username = $config['whmcs_username'];
$whmcs_password = $config['whmcs_password'];
$department_mapping = $config['department_mapping'];
$default_priority = $config['default_priority'];
$enable_logging = $config['enable_logging'];
$debug_mode = $config['debug_mode'];

// Enable error reporting based on debug mode
if ($debug_mode) {
    error_reporting(E_ALL);
    ini_set('display_errors', 1);
} else {
    error_reporting(0);
    ini_set('display_errors', 0);
}

// Set content type
header('Content-Type: application/json');

// Check if request is POST
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    http_response_code(405);
    echo json_encode(['success' => false, 'error' => 'Method not allowed']);
    exit;
}

// Get POST data
$input = json_decode(file_get_contents('php://input'), true);

// Validate required fields
$required_fields = ['first_name', 'last_name', 'email', 'subject', 'message'];
foreach ($required_fields as $field) {
    if (empty($input[$field])) {
        http_response_code(400);
        echo json_encode(['success' => false, 'error' => "Missing required field: $field"]);
        exit;
    }
}

// Sanitize input data
$first_name = sanitize($input['first_name']);
$last_name = sanitize($input['last_name']);
$email = filter_var($input['email'], FILTER_SANITIZE_EMAIL);
$phone = sanitize($input['phone'] ?? '');
$subject_category = $input['subject'];
$message = sanitize($input['message']);

// Validate email
if (!filter_var($email, FILTER_VALIDATE_EMAIL)) {
    http_response_code(400);
    echo json_encode(['success' => false, 'error' => 'Invalid email address']);
    exit;
}

// Get department ID
$department_id = $department_mapping[$subject_category] ?? $department_mapping['other'];

// Prepare ticket data for WHMCS API
$ticket_subject = ucfirst($subject_category) . ': ' . $first_name . ' ' . $last_name . ' - Contact Form Inquiry';
$ticket_message = "Contact Form Submission\n\n";
$ticket_message .= "Name: {$first_name} {$last_name}\n";
$ticket_message .= "Email: {$email}\n";
if (!empty($phone)) {
    $ticket_message .= "Phone: {$phone}\n";
}
$ticket_message .= "Category: " . ucfirst($subject_category) . "\n\n";
$ticket_message .= "Message:\n{$message}";

// WHMCS API parameters
$api_params = [
    'action' => 'OpenTicket',
    'username' => $whmcs_username,
    'password' => $whmcs_password,
    'deptid' => $department_id,
    'subject' => $ticket_subject,
    'message' => $ticket_message,
    'name' => $first_name . ' ' . $last_name,
    'email' => $email,
    'priority' => $default_priority,
    'responsetype' => 'json'
];

// Make API request to WHMCS
$response = callWHMCSAPI($whmcs_url, $api_params);

if ($response && $response['result'] === 'success') {
    // Ticket created successfully
    $ticket_id = $response['tid'];
    
    // Log successful ticket creation
    if ($enable_logging) {
        error_log("WHMCS Ticket created successfully. Ticket ID: {$ticket_id}, Email: {$email}");
    }
    
    echo json_encode([
        'success' => true,
        'ticket_id' => $ticket_id,
        'message' => 'Your message has been sent successfully. Ticket #' . $ticket_id . ' has been created.'
    ]);
} else {
    // Handle API errors
    $error_message = $response['message'] ?? 'Unknown error occurred while creating ticket';
    
    // Log the error
    if ($enable_logging) {
        error_log("WHMCS API Error: " . $error_message . " - Email: {$email}");
    }
    
    // Return error response
    http_response_code(500);
    echo json_encode([
        'success' => false,
        'error' => 'Unable to create support ticket. Please try again or contact support directly.',
        'debug' => $debug_mode ? $error_message : null
    ]);
}

/**
 * Make API call to WHMCS
 */
function callWHMCSAPI($url, $params) {
    $ch = curl_init();
    curl_setopt($ch, CURLOPT_URL, $url);
    curl_setopt($ch, CURLOPT_POST, 1);
    curl_setopt($ch, CURLOPT_TIMEOUT, 30);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, 1);
    curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, 1);
    curl_setopt($ch, CURLOPT_POSTFIELDS, http_build_query($params));
    
    $data = curl_exec($ch);
    
    if (curl_error($ch)) {
        error_log("CURL Error: " . curl_error($ch));
        curl_close($ch);
        return false;
    }
    
    curl_close($ch);
    
    return json_decode($data, true);
}

/**
 * Sanitize input data
 */
function sanitize($input) {
    return htmlspecialchars(strip_tags(trim($input)), ENT_QUOTES, 'UTF-8');
}
?>